# DateTime Fix Guide

This guide explains the solution implemented to fix the database error related to invalid datetime values in the `post_modified` column and other datetime fields.

## Problem Description

The error occurred when trying to query the database:
```
Invalid `prisma.posts.findFirst()` invocation: Value out of range for the type. The column `post_modified` contained an invalid datetime value with either day or month set to zero.
```

This typically happens when the database contains datetime values like:
- `0000-00-00 00:00:00`
- `2024-00-15 10:30:00` (month set to zero)
- `2024-01-00 10:30:00` (day set to zero)

## Root Cause

The issue stems from MySQL's historical handling of invalid datetime values. In older MySQL versions or certain configurations, invalid datetime values like `0000-00-00 00:00:00` were allowed as default values. These values are now rejected by modern MySQL versions and Prisma.

## Solution Components

### 1. DateTime Utilities (`src/lib/datetime-utils.ts`)

A comprehensive set of utilities for handling datetime validation:

- `isValidDateString()` - Validates YYYY-MM-DD format dates
- `isInvalidDateTime()` - Detects common invalid MySQL datetime patterns
- `sanitizeDateTime()` - Cleans and validates datetime values
- `createSafeDateTime()` - Creates safe datetime with fallback
- `validateDateTimeForDB()` - Validates datetime before database operations
- `formatForMySQL()` - Formats datetime for MySQL insertion
- `sanitizeDateTimeFields()` - Batch sanitizes datetime fields in objects

### 2. Safe Prisma Operations (`src/lib/prisma-safe.ts`)

Wrapper functions that handle datetime validation errors gracefully:

- `safeFindFirst()` - Safe wrapper for findFirst operations
- `safeFindMany()` - Safe wrapper for findMany operations
- `safeCreate()` - Safe wrapper for create operations with validation
- `safeUpdate()` - Safe wrapper for update operations with validation
- `safePosts` - Pre-configured safe operations for Posts model
- `safeProjects` - Pre-configured safe operations for Projects model

### 3. Database Migration Script (`scripts/fix-invalid-datetimes.ts`)

A comprehensive migration script that:

- Identifies invalid datetime values across all tables
- Provides detailed reporting of found issues
- Fixes invalid values with appropriate replacements
- Handles different table structures and ID fields

### 4. Enhanced Kobo Integration (`src/lib/kobo.ts`)

Updated the Kobo project insertion to use safe datetime handling:

- All datetime fields are validated before database insertion
- Uses `validateDateTimeForDB()` for strict validation
- Implements safe Prisma operations for querying existing posts
- Provides detailed error messages for datetime issues

## Usage

### Running the Migration

To fix existing invalid datetime values in your database:

```bash
npm run fix-datetimes
```

This will:
1. Scan all tables for invalid datetime values
2. Report what was found
3. Fix the invalid values
4. Provide a summary of changes made

### Using Safe Operations in Code

Instead of direct Prisma operations, use the safe wrappers:

```typescript
import { safePosts } from "@/lib/prisma-safe";

// Instead of: prisma.posts.findFirst(...)
const post = await safePosts.findFirst({
  where: { post_title: "Some Title" }
});

// Instead of: prisma.posts.create(...)
const newPost = await safePosts.create({
  data: {
    post_title: "New Post",
    post_date: new Date(),
    // ... other fields
  }
});
```

### Using DateTime Utilities

```typescript
import { sanitizeDateTime, validateDateTimeForDB } from "@/lib/datetime-utils";

// Sanitize a potentially invalid datetime
const safeDate = sanitizeDateTime(someDateTime);

// Validate before database insertion
const validDate = validateDateTimeForDB(someDateTime, 'post_modified', false);
```

## Prevention

To prevent future datetime issues:

1. **Always validate datetime inputs** before database operations
2. **Use the safe Prisma wrappers** for database operations
3. **Sanitize external data** using the datetime utilities
4. **Set up proper database constraints** to prevent invalid datetime insertion

## Common Invalid Patterns

The solution detects and fixes these common invalid datetime patterns:

- `0000-00-00` and `0000-00-00 00:00:00`
- Any date starting with `0000-`
- Any date with `-00-` (zero month)
- Any date with `-00 ` (zero day)
- Dates with years outside reasonable range (1900-2100)

## Error Handling

The solution provides enhanced error messages that include:

- Clear description of the datetime validation error
- Suggested solutions
- Data involved in the operation
- Instructions for running the migration script

## Testing

After implementing the solution:

1. Run the migration script to fix existing data
2. Test Kobo project insertion functionality
3. Verify that all datetime operations work correctly
4. Check that error messages are helpful and actionable

## Maintenance

- Monitor logs for datetime validation warnings
- Regularly check for new invalid datetime patterns
- Update validation rules as needed
- Keep the migration script updated for new table structures

## Files Modified/Created

- `src/lib/datetime-utils.ts` - New datetime utilities
- `src/lib/prisma-safe.ts` - New safe Prisma operations
- `scripts/fix-invalid-datetimes.ts` - New migration script
- `src/lib/kobo.ts` - Updated with safe datetime handling
- `package.json` - Added migration script
- `docs/DATETIME_FIX_GUIDE.md` - This documentation

## Support

If you encounter datetime-related errors:

1. Check the error message for specific guidance
2. Run the migration script if you haven't already
3. Verify your data doesn't contain invalid datetime values
4. Use the safe Prisma operations for database queries
5. Consult this guide for implementation details
