{"name": "my-project-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "fix-datetimes": "tsx scripts/fix-invalid-datetimes.ts", "test-datetime-fix": "tsx scripts/test-datetime-fix.ts"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^7.1.2", "@mui/x-data-grid": "^8.5.3", "@prisma/client": "^6.10.1", "axios": "^1.10.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "next": "15.3.4", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "slugify": "^1.6.6", "uuid": "^11.1.0", "zod": "^3.25.75"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/crypto-js": "^4.2.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}