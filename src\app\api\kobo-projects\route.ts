import { NextResponse } from "next/server";
import { fetchKoboProjectsAll, testKoboConnection, insertKoboProject } from "@/lib/kobo";

export async function GET() {
  try {
    const koboResult = await testKoboConnection();
    if (koboResult.status === "error") {
      console.error("Erreur Kobo :", koboResult.message);
      return NextResponse.json({ error: koboResult.message }, { status: 500 });
    }

    const { status, message, data: koboProjects } = await fetchKoboProjectsAll();
    if (status === "error") {
      console.error(
        "Erreur lors de la récupération des projets Kobo :",
        message
      );
      return NextResponse.json({ error: message }, { status: 500 });
    }

    return NextResponse.json(koboProjects);
  } catch (error) {
    if (error instanceof Error) {
      console.error(
        "Erreur lors de la récupération des projets Kobo :",
        error.message
      );
      return NextResponse.json({ error: error.message }, { status: 500 });
    } else {
      console.error(
        "Erreur lors de la récupération des projets Kobo :",
        error
      );
      return NextResponse.json({ error: String(error) }, { status: 500 });
    }
  }
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    //const userId = 1029; // À adapter selon l'utilisateur connecté
    const projects = Array.isArray(body) ? body : [body];
    const results = [];
    for (const project of projects) {
      try {
        await insertKoboProject(project);
        results.push({ success: true, project: project.title || project.id });
      } catch (e) {
        results.push({ success: false, project: project.title || project.id, error: e instanceof Error ? e.message : String(e) });
      }
    }
    return NextResponse.json({ results });
  } catch (error) {
    return NextResponse.json({ error: error instanceof Error ? error.message : String(error) }, { status: 400 });
  }
}
