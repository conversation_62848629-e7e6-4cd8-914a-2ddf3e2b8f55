/**
 * Test script to verify the datetime fix solution works correctly
 */

import { 
  isValidDateString, 
  isInvalidDateTime, 
  sanitizeDateTime, 
  createSafeDateTime,
  validateDateTimeForDB,
  formatForMySQL,
  sanitizeDateTimeFields
} from "../src/lib/datetime-utils";
import { safePosts, handlePrismaDateTimeError } from "../src/lib/prisma-safe";

/**
 * Test datetime utility functions
 */
function testDateTimeUtils() {
  console.log("🧪 Testing DateTime Utilities...\n");
  
  // Test isValidDateString
  console.log("Testing isValidDateString:");
  const validDates = ["2024-01-15", "2023-12-31", "2025-06-01"];
  const invalidDates = ["2024-00-15", "2024-01-00", "0000-00-00", "invalid", ""];
  
  validDates.forEach(date => {
    const result = isValidDateString(date);
    console.log(`  ✅ ${date}: ${result} (expected: true)`);
  });
  
  invalidDates.forEach(date => {
    const result = isValidDateString(date);
    console.log(`  ❌ ${date}: ${result} (expected: false)`);
  });
  
  // Test isInvalidDateTime
  console.log("\nTesting isInvalidDateTime:");
  const invalidDateTimes = [
    "0000-00-00",
    "0000-00-00 00:00:00",
    "2024-00-15 10:30:00",
    "2024-01-00 10:30:00",
    new Date("invalid")
  ];
  
  invalidDateTimes.forEach(date => {
    const result = isInvalidDateTime(date);
    console.log(`  ✅ ${date}: ${result} (expected: true)`);
  });
  
  // Test sanitizeDateTime
  console.log("\nTesting sanitizeDateTime:");
  const testDates = [
    "2024-01-15 10:30:00",
    "0000-00-00 00:00:00",
    "2024-00-15 10:30:00",
    new Date(),
    null,
    undefined
  ];
  
  testDates.forEach(date => {
    const result = sanitizeDateTime(date);
    console.log(`  📅 ${date} → ${result}`);
  });
  
  // Test createSafeDateTime
  console.log("\nTesting createSafeDateTime:");
  const fallback = new Date("2024-01-01");
  const testSafeDates = [
    "2024-01-15 10:30:00",
    "0000-00-00 00:00:00",
    null
  ];
  
  testSafeDates.forEach(date => {
    const result = createSafeDateTime(date, fallback);
    console.log(`  🛡️ ${date} → ${result.toISOString()}`);
  });
  
  // Test formatForMySQL
  console.log("\nTesting formatForMySQL:");
  const mysqlTestDates = [
    new Date("2024-01-15T10:30:00Z"),
    "2024-01-15 10:30:00",
    "0000-00-00 00:00:00",
    null
  ];
  
  mysqlTestDates.forEach(date => {
    const result = formatForMySQL(date);
    console.log(`  🗄️ ${date} → ${result}`);
  });
  
  // Test sanitizeDateTimeFields
  console.log("\nTesting sanitizeDateTimeFields:");
  const testObject = {
    id: 1,
    title: "Test Post",
    post_date: "2024-01-15 10:30:00",
    post_modified: "0000-00-00 00:00:00",
    other_field: "not a date"
  };
  
  const sanitized = sanitizeDateTimeFields(testObject, ['post_date', 'post_modified']);
  console.log(`  📦 Original: ${JSON.stringify(testObject, null, 2)}`);
  console.log(`  📦 Sanitized: ${JSON.stringify(sanitized, null, 2)}`);
}

/**
 * Test validateDateTimeForDB function
 */
function testValidateForDB() {
  console.log("\n🧪 Testing validateDateTimeForDB...\n");
  
  const testCases = [
    { value: new Date(), field: "valid_date", allowNull: false, shouldPass: true },
    { value: "2024-01-15 10:30:00", field: "valid_string", allowNull: false, shouldPass: true },
    { value: "0000-00-00 00:00:00", field: "invalid_date", allowNull: false, shouldPass: false },
    { value: null, field: "null_allowed", allowNull: true, shouldPass: true },
    { value: null, field: "null_not_allowed", allowNull: false, shouldPass: false },
  ];
  
  testCases.forEach(({ value, field, allowNull, shouldPass }) => {
    try {
      const result = validateDateTimeForDB(value, field, allowNull);
      if (shouldPass) {
        console.log(`  ✅ ${field}: ${value} → ${result} (PASS)`);
      } else {
        console.log(`  ❌ ${field}: ${value} → ${result} (UNEXPECTED PASS)`);
      }
    } catch (error) {
      if (!shouldPass) {
        console.log(`  ✅ ${field}: ${value} → Error: ${(error as Error).message.split('\n')[0]} (EXPECTED FAIL)`);
      } else {
        console.log(`  ❌ ${field}: ${value} → Error: ${(error as Error).message} (UNEXPECTED FAIL)`);
      }
    }
  });
}

/**
 * Test error handling
 */
function testErrorHandling() {
  console.log("\n🧪 Testing Error Handling...\n");
  
  // Test handlePrismaDateTimeError
  const mockError = {
    message: "Invalid `prisma.posts.findFirst()` invocation: Value out of range for the type. The column `post_modified` contained an invalid datetime value"
  };
  
  try {
    handlePrismaDateTimeError(mockError, "test operation", { test: "data" });
  } catch (error) {
    console.log("  ✅ Enhanced error message generated:");
    console.log(`     ${(error as Error).message.split('\n')[0]}`);
  }
  
  // Test with non-datetime error
  const normalError = { message: "Some other error" };
  
  try {
    handlePrismaDateTimeError(normalError, "test operation");
  } catch (error) {
    console.log("  ✅ Normal error passed through:");
    console.log(`     ${(error as Error).message}`);
  }
}

/**
 * Test safe Prisma operations (mock test)
 */
function testSafePrismaOperations() {
  console.log("\n🧪 Testing Safe Prisma Operations (Mock)...\n");
  
  // Since we can't easily test actual database operations in this script,
  // we'll just verify the functions exist and are callable
  
  console.log("  ✅ safePosts.findFirst exists:", typeof safePosts.findFirst === 'function');
  console.log("  ✅ safePosts.findMany exists:", typeof safePosts.findMany === 'function');
  console.log("  ✅ safePosts.create exists:", typeof safePosts.create === 'function');
  console.log("  ✅ safePosts.update exists:", typeof safePosts.update === 'function');
  
  console.log("\n  📝 Note: Actual database operations should be tested in integration tests");
}

/**
 * Main test function
 */
async function main() {
  console.log("🚀 Starting DateTime Fix Solution Tests\n");
  console.log("=" .repeat(60));
  
  try {
    testDateTimeUtils();
    testValidateForDB();
    testErrorHandling();
    testSafePrismaOperations();
    
    console.log("\n" + "=".repeat(60));
    console.log("✅ All tests completed successfully!");
    console.log("\n📋 Next steps:");
    console.log("1. Run the migration script: npm run fix-datetimes");
    console.log("2. Test actual database operations with your application");
    console.log("3. Monitor logs for any datetime validation warnings");
    
  } catch (error) {
    console.error("\n❌ Test failed:", error);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  main();
}

export { main as testDateTimeFix };
