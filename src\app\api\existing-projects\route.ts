import { NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

export async function GET() {
  try {
    if (!prisma) {
      return NextResponse.json({ error: "Erreur de connexion à la base de données" }, { status: 500 });
    }
    const projects = await prisma.projects.findMany({
      select: {
        id: true,
        title: true,
        status: true,
        overall_budget: true,
        created_at: true,
      },
      orderBy: {
        created_at: "desc",
      },
    });

    // Conversion BigInt -> string pour l'id
    const projectsWithStringId = projects.map((p) => ({ ...p, id: p.id.toString() }));
    return NextResponse.json(projectsWithStringId);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : "Une erreur inconnue s'est produite";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
