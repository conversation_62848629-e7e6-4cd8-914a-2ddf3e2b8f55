import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function GET() {
  // Agencies
  const agencies = await prisma.termTaxonomy.findMany({
    where: { taxonomy: "agency" },
    include: { term: true },
  });
  // Donors
  const donors = await prisma.termTaxonomy.findMany({
    where: { taxonomy: "donor" },
    include: { term: true },
  });
  // Effects
  const effects = await prisma.termTaxonomy.findMany({
    where: { taxonomy: "effect" },
    include: { term: true },
  });
  // Zones
  const zones = await prisma.termTaxonomy.findMany({
    where: { taxonomy: { in: ["region", "department", "commune"] } },
    include: { term: true },
  });
  // Users
  const users = await prisma.users.findMany({
    select: { ID: true, display_name: true }
  });
  // Sectors
  const sectors = await prisma.termTaxonomy.findMany({
    where: { taxonomy: "sector" },
    include: { term: true },
  });
  // SDGs
  const sdgs = await prisma.termTaxonomy.findMany({
    where: { taxonomy: "sdg" },
    include: { term: true },
  });
  // Products
  const products = await prisma.termTaxonomy.findMany({
    where: { taxonomy: "product" },
    include: { term: true },
  });

  return NextResponse.json({
    agencies: agencies.map(a => ({ id: a.term_id, name: a.term.name })),
    donors: donors.map(d => ({ id: d.term_id, name: d.term.name })),
    effects: effects.map(e => ({ id: e.term_id, name: e.term.name })),
    zones: zones.map(z => ({
      id: z.term_id,
      name: z.term.name,
      taxonomy: z.taxonomy,
      parent: z.parent,
      term_taxonomy_id: z.term_taxonomy_id
    })),
    users: users.map(u => ({ id: u.ID, name: u.display_name })),
    sectors: sectors.map(s => ({ id: s.term_id, name: s.term.name })),
    sdgs: sdgs.map(s => ({ id: s.term_id, name: s.term.name })),
    products: products.map(p => ({ id: p.term_id, name: p.term.name })),
  });
} 