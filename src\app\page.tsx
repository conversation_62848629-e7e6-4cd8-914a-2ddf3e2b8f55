"use client";

import { useEffect, useState } from "react";
import axios from "axios";

// Interface pour KoboProjectDTO, basée sur kobo.ts
interface KoboProjectDTO {
  id: string | number;
  title: string;
  description: string;
  type: string;
  status: string;
  progress: string;
  start_date: string;
  end_date: string;
  has_extension: boolean;
  extension_dates: string[];
  agencies: {
    name: string;
    contact: { name: string; title: string; email: string };
    received: number;
    disbursed: number;
  }[];
  donor: string;
  budget: number;
  mobilized: number;
  disbursed: number;
  effects: string[];
  products: string[];
  target_population: string[];
  sectors: string[];
  sdgs: string[];
  regions: string[];
  departments: string[];
  communes: string[];
}

export default function Home() {
  const [mysqlStatus, setMysqlStatus] = useState<string>(
    "Vérification en cours..."
  );
  const [koboStatus, setKoboStatus] = useState<string>(
    "Vérification en cours..."
  );
  const [projects, setProjects] = useState<KoboProjectDTO[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [syncStatus, setSyncStatus] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const projectsPerPage = 5;
  const [syncReport, setSyncReport] = useState<any>(null);
  const [syncFilter, setSyncFilter] = useState<'all' | 'inserted' | 'ignored' | 'error' | 'updated'>('all');

  useEffect(() => {
    // Utilise l'API /api/check-connections pour vérifier les connexions côté client
    axios
      .get("/api/check-connections")
      .then((res) => {
        setMysqlStatus(res.data?.mysql?.message || "Erreur MySQL");
        setKoboStatus(res.data?.kobo?.message || "Erreur Kobo");
      })
      .catch(() => {
        setMysqlStatus("Erreur de connexion MySQL");
        setKoboStatus("Erreur de connexion Kobo Toolbox");
      });
    // Récupérer les projets Kobo
    axios
      .get("/api/kobo-projects")
      .then((res) => {
        console.log("Projets Kobo reçus :", res.data);
        setProjects(res.data);
        setLoading(false);
      })
      .catch((err) => {
        console.error("Erreur lors de la récupération des projets Kobo :", err);
        setLoading(false);
      });
  }, []);

  // Calculer les projets à afficher pour la page actuelle
  const indexOfLastProject = currentPage * projectsPerPage;
  const indexOfFirstProject = indexOfLastProject - projectsPerPage;
  const currentProjects = projects.slice(
    indexOfFirstProject,
    indexOfLastProject
  );
  const totalPages = Math.ceil(projects.length / projectsPerPage);

  // Changer de page
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Page précédente
  const handlePrevious = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Page suivante
  const handleNext = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Générer les numéros de page à afficher
  const getPageNumbers = () => {
    const pageNumbers: number[] = [];
    const maxPagesToShow = 5;
    const halfWindow = Math.floor(maxPagesToShow / 2);
    let startPage = Math.max(1, currentPage - halfWindow);
    const endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }
    return pageNumbers;
  };

  const handleSync = async () => {
    setSyncStatus("Synchronisation en cours...");
    setSyncReport(null);
    try {
      const res = await axios.post("/api/sync-kobo-projects");
      setSyncStatus(
        `Synchronisation terminée : ${res.data.inserted} projets insérés, ${res.data.errors} erreurs, ${res.data.ignored || 0} ignorés, ${res.data.updated || 0} mis à jour`
      );
      setSyncReport(res.data);
      // Recharger les projets après synchronisation
      const projectsRes = await axios.get("/api/kobo-projects");
      setProjects(projectsRes.data);
    } catch (error) {
      setSyncStatus("Erreur lors de la synchronisation");
      setSyncReport(null);
      console.error("Erreur lors de la synchronisation :", error);
    }
  };

  const handleRetry = async (id: string | number) => {
    setSyncStatus(`Relance du projet ${id} en cours...`);
    try {
      const res = await fetch(`/api/sync-kobo-projects/${id}`, { method: 'POST' });
      const data = await res.json();
      setSyncStatus(
        data.success
          ? `Projet ${id} relancé avec succès`
          : `Erreur lors de la relance du projet ${id} : ${data.error}`
      );
      // Optionnel : rafraîchir le rapport de synchronisation
      if (syncReport) {
        // Relancer la synchro pour mettre à jour le rapport
        const resSync = await axios.post("/api/sync-kobo-projects");
        setSyncReport(resSync.data);
      }
    } catch (error) {
      setSyncStatus(`Erreur lors de la relance du projet ${id}`);
    }
  };

  const filteredSyncDetails = syncReport?.details?.filter(
    (row: any) => syncFilter === 'all' || row.status === syncFilter
  ) || [];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-blue-100 flex flex-col items-center justify-center p-4">
      <div className="w-full max-w-8xl bg-white rounded-xl shadow-lg p-8 flex flex-col gap-6">
        <h1 className="text-3xl font-bold text-blue-700 text-center mb-4">
          Statut des connexions
        </h1>
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-3 p-4 rounded-lg border border-blue-100 bg-blue-50">
            <span className="font-semibold text-blue-600">
              Connexion MySQL :
            </span>
            <span
              className={
                mysqlStatus.includes("réussie")
                  ? "text-green-600 font-medium"
                  : "text-red-600 font-medium"
              }
            >
              {mysqlStatus}
            </span>
          </div>
          <div className="flex items-center gap-3 p-4 rounded-lg border border-blue-100 bg-blue-50">
            <span className="font-semibold text-blue-600">
              Connexion Kobo Toolbox :
            </span>
            <span
              className={
                koboStatus.includes("réussie")
                  ? "text-green-600 font-medium"
                  : "text-red-600 font-medium"
              }
            >
              {koboStatus}
            </span>
          </div>
        </div>

        <button
          onClick={handleSync}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Synchroniser les projets
        </button>
        {syncStatus && (
          <p className="text-center text-gray-600">{syncStatus}</p>
        )}
        {/* Rapport détaillé de synchronisation Kobo */}
        {syncReport?.details && (
          <div className="mt-8 w-full">
            <h2 className="text-xl font-semibold mb-2">Rapport détaillé de synchronisation</h2>
            <div className="mb-4">
              <span className="mr-4">Insérés : {syncReport.inserted}</span>
              <span className="mr-4">Ignorés : {syncReport.ignored}</span>
              <span className="mr-4">Erreurs : {syncReport.errors}</span>
              <span className="mr-4">Mises à jour : {syncReport.updated}</span>
            </div>
            <div className="mb-4">
              <label>Filtrer : </label>
              <select value={syncFilter} onChange={e => setSyncFilter(e.target.value as any)} className="border rounded px-2 py-1">
                <option value="all">Tous</option>
                <option value="inserted">Insérés</option>
                <option value="ignored">Ignorés</option>
                <option value="error">Erreurs</option>
                <option value="updated">Mises à jour</option>
              </select>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full border">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border px-2 py-1">ID</th>
                    <th className="border px-2 py-1">Statut</th>
                    <th className="border px-2 py-1">Raison/Erreur</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredSyncDetails.map((row: any) => (
                    <tr key={row.id}>
                      <td className="border px-2 py-1">{row.id}</td>
                      <td className="border px-2 py-1">
                        {row.status === 'error' && (
                          <button
                            className="px-2 py-1 bg-yellow-500 text-white rounded"
                            onClick={() => handleRetry(row.id)}
                          >
                            Relancer
                          </button>
                        )}
                        {row.status}
                      </td>
                      <td className="border px-2 py-1">{row.reason || row.error || ''}</td>
                    </tr>
                  ))}
                  {filteredSyncDetails.length === 0 && (
                    <tr>
                      <td colSpan={4} className="text-center py-2">Aucun résultat</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        <h2 className="text-2xl font-semibold text-blue-700 text-center mt-6">
          Liste des projets
        </h2>
        {loading ? (
          <p className="text-center text-gray-600">Chargement des projets...</p>
        ) : projects.length === 0 ? (
          <p className="text-center text-gray-600">Aucun projet trouvé.</p>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-blue-100 text-blue-800">
                    <th className="p-3 text-left font-semibold border-b border-blue-200">
                      Titre
                    </th>
                    <th className="p-3 text-left font-semibold border-b border-blue-200">
                      Type
                    </th>
                    <th className="p-3 text-left font-semibold border-b border-blue-200">
                      Statut
                    </th>
                    <th className="p-3 text-left font-semibold border-b border-blue-200">
                      Progression
                    </th>
                    <th className="p-3 text-left font-semibold border-b border-blue-200">
                      Date de début
                    </th>
                    <th className="p-3 text-left font-semibold border-b border-blue-200">
                      Date de fin
                    </th>
                    <th className="p-3 text-left font-semibold border-b border-blue-200">
                      Budget (USD)
                    </th>
                    <th className="p-3 text-left font-semibold border-b border-blue-200">
                      Agences
                    </th>
                    <th className="p-3 text-left font-semibold border-b border-blue-200">
                      Régions
                    </th>
                    <th className="p-3 text-left font-semibold border-b border-blue-200">
                      Secteurs
                    </th>
                    <th className="p-3 text-left font-semibold border-b border-blue-200">
                      Effets
                    </th>
                    <th className="p-3 text-left font-semibold border-b border-blue-200">
                      Produits
                    </th>
                    <th className="p-3 text-left font-semibold border-b border-blue-200">
                      Population cible
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {currentProjects.map((project) => (
                    <tr key={project.id} className="hover:bg-blue-50">
                      <td className="p-3 border-b border-blue-100">
                        {project.title}
                      </td>
                      <td className="p-3 border-b border-blue-100">
                        {project.type}
                      </td>
                      <td className="p-3 border-b border-blue-100">
                        {project.status}
                      </td>
                      <td className="p-3 border-b border-blue-100">
                        {project.progress}
                      </td>
                      <td className="p-3 border-b border-blue-100">
                        {project.start_date}
                      </td>
                      <td className="p-3 border-b border-blue-100">
                        {project.end_date}
                      </td>
                      <td className="p-3 border-b border-blue-100">
                        {project.budget.toLocaleString()}
                      </td>
                      <td className="p-3 border-b border-blue-100">
                        {project.agencies.length > 0
                          ? project.agencies.map((a) => a.name).join(", ")
                          : "Aucune"}
                      </td>
                      <td className="p-3 border-b border-blue-100">
                        {project.regions.length > 0
                          ? project.regions.join(", ")
                          : "Aucune"}
                      </td>
                      <td className="p-3 border-b border-blue-100">
                        {project.sectors.length > 0
                          ? project.sectors.join(", ")
                          : "Aucun"}
                      </td>
                      <td className="p-3 border-b border-blue-100">
                        {project.effects.length > 0
                          ? project.effects.join(", ")
                          : "Aucun"}
                      </td>
                      <td className="p-3 border-b border-blue-100">
                        {project.products.length > 0
                          ? project.products.join(", ")
                          : "Aucun"}
                      </td>
                      <td className="p-3 border-b border-blue-100">
                        {project.target_population.length > 0
                          ? project.target_population.join(", ")
                          : "Aucune"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="flex justify-center items-center gap-2 mt-4">
              <button
                onClick={handlePrevious}
                disabled={currentPage === 1}
                className={`px-4 py-2 rounded-lg border ${
                  currentPage === 1
                    ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                    : "bg-blue-600 text-white hover:bg-blue-700"
                }`}
              >
                Précédent
              </button>
              {getPageNumbers().map((pageNumber) => (
                <button
                  key={pageNumber}
                  onClick={() => handlePageChange(pageNumber)}
                  className={`px-4 py-2 rounded-lg border ${
                    currentPage === pageNumber
                      ? "bg-blue-600 text-white"
                      : "bg-white text-blue-600 hover:bg-blue-50"
                  }`}
                >
                  {pageNumber}
                </button>
              ))}
              <button
                onClick={handleNext}
                disabled={currentPage === totalPages}
                className={`px-4 py-2 rounded-lg border ${
                  currentPage === totalPages
                    ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                    : "bg-blue-600 text-white hover:bg-blue-700"
                }`}
              >
                Suivant
              </button>
            </div>
            <p className="text-center text-gray-600 mt-2">
              Page {currentPage} sur {totalPages} ({projects.length} projets au
              total)
            </p>
          </>
        )}
      </div>
    </div>
  );
}
