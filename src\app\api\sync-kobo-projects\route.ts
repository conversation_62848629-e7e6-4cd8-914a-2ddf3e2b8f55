// src/app/api/sync-kobo-projects/route.ts
import { NextResponse } from "next/server";
import { fetchKoboProjectsAll, insertKoboProject } from "@/lib/kobo";

export async function POST() {
  try {
    console.log("Début de la synchronisation des projets Kobo...");
    const { status, message, data } = await fetchKoboProjectsAll();
    console.log("Résultat de fetchKoboProjectsAll :", {
      status,
      message,
      projectCount: data.length,
    });
    if (status !== "success") {
      return NextResponse.json({ message }, { status: 500 });
    }

    const results = [];
    type ProjectInsertResult = { success: boolean; error?: string; projectId?: number; };
    for (const project of data) {
      try {
        const result: ProjectInsertResult = await insertKoboProject(project);
        if (result.success) {
          results.push({ id: project.id, status: 'inserted' });
        } else if (result.error && result.error.includes('déjà existant')) {
          results.push({ id: project.id, status: 'ignored', reason: result.error });
        } else if (result.error) {
          results.push({ id: project.id, status: 'error', error: result.error });
        } else {
          results.push({ id: project.id, status: 'error', error: 'Erreur inconnue' });
        }
      } catch (projectError) {
        results.push({
          id: project.id,
          status: 'error',
          error: projectError instanceof Error ? projectError.message : String(projectError),
        });
      }
    }

    const inserted = results.filter((r) => r.status === 'inserted').length;
    const ignored = results.filter((r) => r.status === 'ignored').length;
    const errors = results.filter((r) => r.status === 'error').length;
    const updated = results.filter((r) => r.status === 'updated').length;

    // Log détaillé pour chaque projet en erreur
    type ProjectErrorResult = { status: 'error'; id: string | number; error: string; };
    (results as ProjectErrorResult[]).filter((r) => r.status === 'error').forEach((r) => {
      const failedProject = data.find((p: { id: string | number }) => p.id === r.id);
      console.error(`Détail du projet non inséré (ID: ${r.id}):`, JSON.stringify(failedProject, null, 2));
      console.error(`Détail de l'objet erreur pour le projet ${r.id}:`, JSON.stringify(r, null, 2));
      console.error(`Erreur d'insertion pour le projet ${r.id} : ${r.error}`);
    });

    console.log(`Synchronisation terminée : ${inserted} projets insérés, ${ignored} ignorés, ${errors} erreurs, ${updated} mis à jour`);

    return NextResponse.json({
      message: "Synchronisation terminée",
      inserted,
      ignored,
      errors,
      updated,
      details: results,
    });
  } catch (error) {
    console.error("Erreur lors de la synchronisation :", error);
    return NextResponse.json(
      { message: "Erreur lors de la synchronisation des projets" },
      { status: 500 }
    );
  }
}
