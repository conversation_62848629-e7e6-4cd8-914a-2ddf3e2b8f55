import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

export async function GET() {
  // On suppose que les taxonomies pour les zones sont 'region', 'department', 'commune'
  const zones = await prisma.termTaxonomy.findMany({
    where: { taxonomy: { in: ["region", "department", "commune"] } },
    include: { term: true },
  });
  return NextResponse.json(
    zones.map(z => ({
      id: z.term_id,
      name: z.term.name,
      taxonomy: z.taxonomy,
      parent: z.parent,
      term_taxonomy_id: z.term_taxonomy_id
    }))
  );
} 