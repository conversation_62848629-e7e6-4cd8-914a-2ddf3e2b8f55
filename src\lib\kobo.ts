import axios from "axios";
//import { AxiosError } from "axios";
import prisma from "@/lib/prisma";
import slugify from "slugify";
import type { Prisma } from "@/generated/prisma";
import { parse } from "date-fns";
import { validateKoboProject } from "@/lib/validation";
import CryptoJS from "crypto-js";

const KOBO_API_URL = "https://kf.kobotoolbox.org/api/v2";
const KOBO_TOKEN = process.env.KOBO_TOKEN;
const KOBO_ASSET_ID = process.env.KOBO_ASSET_ID;

const koboClient = axios.create({
  baseURL: KOBO_API_URL,
  headers: {
    Authorization: `Token ${KOBO_TOKEN}`,
  },
});

export async function testKoboConnection() {
  if (!KOBO_TOKEN || !KOBO_ASSET_ID) {
    console.error(
      "Variables d'environnement KOBO_TOKEN ou KOBO_ASSET_ID manquantes"
    );
    return { status: "error", message: "Configuration Ko<PERSON> invalide" };
  }
  try {
    await koboClient.get(`/assets/${KOBO_ASSET_ID}/`);
    return { status: "success", message: "Connexion Kobo Toolbox réussie" };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      status: "error",
      message: `Échec de la connexion Kobo Toolbox : ${errorMessage}`,
    };
  }
}

type KoboEntry = Record<string, string>;

interface KoboProjectDTO {
  id: string | number;
  title: string;
  description: string;
  type: string;
  status: string;
  progress: string;
  start_date: string;
  end_date: string;
  has_extension: boolean;
  extension_dates: string[];
  agencies: AgencyDTO[];
  donor: string;
  budget: number;
  mobilized: number;
  disbursed: number;
  effects: string[];
  products: string[];
  target_population: string[];
  sectors: string[];
  sdgs: string[];
  regions: string[];
  departments: string[];
  communes: string[];
}

interface AgencyDTO {
  name: string;
  contact: {
    name: string;
    title: string;
    email: string;
  };
  received: number;
  disbursed: number;
}

// Mappages pour formater les données
const typeMap: { [key: string]: string } = {
  programme_conjoint: "joint",
  programme_r_gulier: "regular",
};

const statusMap: { [key: string]: string } = {
  en_cours: "open",
  cl_turer: "closed",
};

const progressMap: { [key: string]: string } = {
  en_cours: "on_track",
  compl_te: "completed",
};

const effectMap: { [key: string]: string } = {
  effet_1___d_ici_2027__les_institutions_n: "Effet 1",
  effet_2__d_ici___2027__les_populations_n: "Effet 2",
  effet_3___d_ici_2027__les_populations_le: "Effet 3",
  effet_2: "Effet 2",
};

const productMap: { [key: string]: string } = {
  produit_1_1___les_institutions_nationale: "Produit 1.1",
  produit_1_2____exemple__les_communaut_s_: "Produit 1.2",
  produit_1_3___les_entit_s_d_concentr_es_: "Produit 1.3",
  produit_1_4___progr_s_accomplis_vers_la_: "Produit 1.4",
  produit_1_5___les_institutions_charg_es_: "Produit 1.5",
  produit_1_6___les_institutions_et_organe: "Produit 1.6",
  produit_2_1___les_populations_notamment_: "Produit 2.1",
  produit_2_2____les_populations_notamment: "Produit 2.2",
  produit_2_3____les_acteurs_du_syst_me_de: "Produit 2.3",
  produits_2_4____les_communaut_s_et_les_f: "Produit 2.4",
  produit_2_5____les_acteurs_de_l_ducation: "Produit 2.5",
  produit_2_6____les_partenaires_publics__: "Produit 2.6",
  produit_2_7____les_familles__les_communa: "Produit 2.7",
  produit_2_8____les_services_d_approvisio: "Produit 2.8",
  produit_2_9____les_m_nages__y_compris_le: "Produit 2.9",
  produit_2_10____le_gouvernement__les_mun: "Produit 2.10",
  produit_2_11___les_partenaires_publics_e: "Produit 2.11",
  produit_2_14___les_services_techniques_d: "Produit 2.14",
  produit_2_16___les_services_et_les_acteu: "Produit 2.16",
  produit_2_17___les_communaut_s_et_leurs_: "Produit 2.17",
  produit_3_1___les_populations_et_les_com: "Produit 3.1",
  produit_3_2___les_populations_et_les_com: "Produit 3.2",
  produit_3_3___les_institutions_publiques: "Produit 3.3",
  produit_3_4__les_populations_vuln_rables: "Produit 3.4",
  produit_3_5_l_acc_s_aux__nergies_renouve: "Produit 3.5",
  produit_3_6___les_communaut_s_et_les_pop: "Produit 3.6",
};

const targetPopulationMap: { [key: string]: string } = {
  femmes_filles_hommes_et_gar_ons__toutes_:
    "Femmes, filles, hommes et garçons (toutes les tranches d'âge)",
  jeunes__filles___gar_ons__de_10___35_ans:
    "Jeunes (filles & garçons) de 10 – 35 ans",
  personnes_vivant_avec_un_handicap__toute:
    "Personnes vivant avec un handicap (toutes les tranches d'ages)",
  les_communaut_s__femmes__hommes__jeunes_:
    "Les communautés (les femmes, les hommes, les jeunes, les adolescent(e)s et les enfants)",
  les_leaders_communautaires_traditionnels:
    "Les leaders communautaires, traditionnels et religieux (Femmes médiatrices, chefferies traditionnelles et leaders religieux, leaders d'opinions, les relais communautaires …)",
  enfants_6_59_mois: "Enfants 6-59 mois",
  adolescent_e_s__10___19_ans__mari_s_ou_n:
    "Adolescent (e)s (10 à 19 ans) mariés ou non, scolarisés ou non",
  femmes_de_15_49_ans_en__ge_de_procr_er:
    "Femmes de 15-49 ans en âge de procréer",
  femmes_et_hommes_travaillant_dans_les_sy:
    "Femmes et hommes travaillant dans les systèmes alimentaires et chaînes de valeurs agricole (agriculture, élevage, pêches, foresterie, …)",
  pme___start_ups: "PME &amp; Start-ups",
  institutions_financi_res: "Institutions financières",
  institutions__tatiques: "Institutions étatiques",
  organisations_de_la_soci_t__civile__osc:
    "Les organisations de la société civile (OSC)",
  institutions_de_formation_publiques_ou_p:
    "Institutions de formation publiques ou privés",
  hommes_ou_femmes_mari_s: "Hommes ou femmes mariés",
  femmes_et_hommes__filles_et_gar_ons_de_t:
    "Femmes et hommes, filles et garçons de tout âge avec ou sans handicap, membres des ménages vulnérables victimes de sécheresse, inondations, conflits/déplacement",
  collectivit_s_territoriales: "Collectivités territoriales",
  institutions_du_secteur_priv: "Institutions du secteur privé",
  _lu_e_s_au_niveau_national_et_local:
    "Les élu-e-s au niveau national et local",
  acteurs_et_actrices_des_m_dias_nationaux:
    "Les acteurs et actrices des médias nationaux, locaux publics et privés",
  personnes_d_plac_es: "Personnes déplacées",
  migrants___refoul_s: "Migrants/Refoulés",
  refugi_s: "Refugiés",
  filles___gar_ons__5___9_ans: "Filles &amp; Garçons (5 à 9 ans)",
  filles___gar_ons__10___19_ans: "Filles &amp; Garçons (10 à 19 ans)",
  les_enfants__adolescents__femmes_enceint:
    "Les enfants, adolescents et femmes enceintes et/ou allaitantes",
  femmes__enfants__nouveau_n_es__jeunes_et:
    "Femmes, enfants, nouveaux nés, jeunes et adolescents",
  femmes__adolescentes_et_filles_victimes_:
    "Femmes, adolescentes et filles, victimes des VBG",
};

const regionMap: { [key: string]: string } = {
  tillab_ri: "Tillabéri",
  ville_de_niamey: "Niamey",
};

const secteurMap: { [key: string]: string } = {
  s_curit__alimentaire: "Sécurité Alimentaire",
  gestion_des_ressources_naturelles___adap:
    "Gestion des ressources naturelles &amp; adaptation au changement climatique",
  emploi___protection_sociale: "Emploi &amp; Protection Sociale",
  economie___d_veloppement: "Economie &amp; Développement",
  sant: "Santé",
  eau_assainissement___hygi_ne: "Eau, Assainissement &amp; Hygiène",
  education___formation_professionnelle:
    "Education &amp; Formation Professionnelle",
  gouvernance_s_curitaire_administrative__:
    "Gouvernance securitaire, administrative &amp; juridique",
  migration___asile: "Migration &amp; Asile",
  protection: "Protection",
};

// Fonction utilitaire pour splitter intelligemment les champs multiples
function smartSplit(val?: string): string[] {
  if (!val) return [];
  // Si présence de virgule, on split dessus
  if (val.includes(",")) {
    return val
      .split(",")
      .map((v) => v.trim())
      .filter((v) => v && v !== "N/A");
  }
  // Sinon, on split sur retour à la ligne
  if (val.includes("\n")) {
    return val
      .split("\n")
      .map((v) => v.trim())
      .filter((v) => v && v !== "N/A");
  }
  // Sinon, on split sur espace si plusieurs mots
  if (val.split(" ").length > 1) {
    return val
      .split(" ")
      .map((v) => v.trim())
      .filter((v) => v && v !== "N/A");
  }
  // Sinon, retourne tel quel si non vide
  return val !== "N/A" ? [val.trim()] : [];
}

export function mapKoboProject(entry: KoboEntry): KoboProjectDTO {
  if (!entry || typeof entry !== "object") {
    throw new Error(
      "Entrée Kobo invalide : 'entry' est undefined ou mal formée."
    );
  }

  const parseDate = (dateStr: string): string => {
    if (!dateStr) return "";
    const trimmed = dateStr.trim();
    if (/^\d{4}-\d{2}-\d{2}/.test(trimmed)) return trimmed.split("T")[0];
    try {
      const parsed = parse(trimmed, "d MMM yyyy", new Date());
      return parsed.toISOString().split("T")[0];
    } catch {
      return "";
    }
  };

  const parseAmount = (value?: string) =>
    value && !isNaN(+value) ? parseFloat(value) : 0;

  const mapType = (label: string): string => {
    const t = label.toLowerCase();
    return typeMap[t] || "other";
  };

  const mapStatus = (status: string): string => {
    const s = status.toLowerCase();
    return statusMap[s] || s || "";
  };

  const mapProgress = (progress: string): string => {
    const p = progress.toLowerCase();
    return progressMap[p] || p || "";
  };

  const mapEffects = (effects: string[]): string[] => {
    return effects.map((e) => effectMap[e.toLowerCase()] || e);
  };

  const mapProducts = (products: string[]): string[] => {
    return products.map((p) => productMap[p.toLowerCase()] || p);
  };

  const mapTargetPopulation = (population: string[]): string[] => {
    return population.map((p) => targetPopulationMap[p.toLowerCase()] || p);
  };

  const mapRegion = (region: string[]): string[] => {
    return region.map((p) => regionMap[p.toLowerCase()] || p);
  };

  const mapSecteur = (secteur: string[]): string[] => {
    return secteur.map((p) => secteurMap[p.toLowerCase()] || p);
  };

  const agencies: AgencyDTO[] = [];

  const agencyMappings = [
    {
      nameKey: "Quelle_est_la_premi_e_en_uvre_du_projet",
      suffix: "", // première agence (pas de suffixe)
    },
    {
      nameKey: "Nom_de_l_agence",
      suffix: "_001",
    },
    {
      nameKey: "Nom_de_l_agence_001",
      suffix: "_002",
    },
  ];

  for (const { nameKey, suffix } of agencyMappings) {
    const name = entry[nameKey];
    if (!name || name.trim() === "") continue;

    const contactName =
      entry[`Le_nom_de_personne_d_contact_de_l_agence${suffix}`]?.trim() ?? "";
    const contactTitle =
      entry[`Le_titre_de_personne_contact_de_l_agence${suffix}`]?.trim() ?? "";
    const contactEmail =
      entry[`L_adresse_mail_de_pe_contact_de_l_agence${suffix}`]?.trim() ?? "";
    const received = parseAmount(
      entry[`Quel_montant_de_fonds_a_t_re_u_en_USD${suffix}`]
    );
    const disbursed = parseAmount(
      entry[`Quel_montant_de_fond_t_d_bours_en_USD${suffix}`]
    );

    agencies.push({
      name: name.trim(),
      contact: {
        name: contactName,
        title: contactTitle,
        email: contactEmail,
      },
      received,
      disbursed,
    });
  }

  const rawEffects = smartSplit(
    entry["Quels_effets_du_Plan_ttendus_de_ce_projet"]
  );
  const rawProducts = smartSplit(
    entry["Quels_produits_du_PC_ojet_va_t_il_g_n_rer"]
  );
  const rawTargetPopulation = smartSplit(
    entry["Qui_sont_les_princip_ciaires_de_ce_projet"]
  );

  const rawRegion = smartSplit(
    entry["Dans_quelles_zones_g_ra_t_il_mis_en_uvre"]
  );

  const rawSecteur = smartSplit(entry["Quels_secteurs_ce_projet_couvre_t_il"]);

  const result = {
    id:
      entry._id && entry["formhub/uuid"]
        ? `${entry._id}_${entry["formhub/uuid"]}`
        : Math.random().toString(36).slice(2),
    title: entry["Quel_est_le_titre_du_projet"]?.trim() || "",
    description:
      entry["Pouvez_vous_fournir_rincipales_activit_s"]?.trim() || "",
    type: mapType(entry["Quelle_est_la_cat_gorie_de_ce_projet"] || ""),
    status: mapStatus(entry["Quel_est_le_statut_actuel_du_projet"] || ""),
    progress: mapProgress(entry["Quelle_est_la_progression_du_projet"] || ""),
    start_date: parseDate(
      entry["quand_est_ce_que_le_projet_a_c"] || "01 jan 2000"
    ),
    end_date: parseDate(
      entry["quand_est_ce_que_le_projet_se"] || "01 jan 2000"
    ),
    has_extension:
      entry["Y_a_t_il_eu_une_prolongation"]?.toLowerCase() === "oui",
    extension_dates: smartSplit(entry["si_oui_quelle_est_la_premi_re"]).map(
      parseDate
    ),
    agencies,
    donor: (entry["Quel_est_le_nom_du_donateur"] || "").trim(),
    budget: parseAmount(entry["Quel_est_le_budget_g_ral_du_projet_en_USD"]),
    mobilized: parseAmount(entry["Quel_est_le_montant_our_ce_projet_en_USD"]),
    disbursed: parseAmount(
      entry["Quel_est_le_montant_our_ce_projet_en_USD_001"]
    ),
    effects: mapEffects(rawEffects),
    products: mapProducts(rawProducts),
    target_population: mapTargetPopulation(rawTargetPopulation),
    sectors: mapSecteur(rawSecteur),
    sdgs: smartSplit(entry["Quels_sont_les_ODDs_cibl_s_par_ce_projet"]),
    regions: mapRegion(rawRegion),
    departments: smartSplit(entry["Quels_sont_les_d_par_uverts_par_ce_projet"]),
    communes: smartSplit(entry["Quelles_communes_son_ibl_es_par_ce_projet"]),
  };
  return result;
}

// Fonction pour récupérer tous les projets Kobo
export async function fetchKoboProjectsAll() {
  if (!KOBO_TOKEN || !KOBO_ASSET_ID) {
    console.error(
      "Variables d'environnement KOBO_TOKEN ou KOBO_ASSET_ID manquantes"
    );
    return {
      status: "error",
      message: "Configuration Kobo invalide",
      data: [],
    };
  }
  try {
    const response = await koboClient.get(`/assets/${KOBO_ASSET_ID}/data.json`);
    let projects = (response.data.results || []).map((item: KoboEntry) =>
      mapKoboProject(item)
    );
    // Filtrer les doublons par id unique
    const seen = new Set();
    projects = projects.filter((p: KoboProjectDTO) => {
      if (!p.id) return false;
      if (seen.has(p.id)) return false;
      seen.add(p.id);
      return true;
    });
    return {
      status: "success",
      message: "Données Kobo récupérées",
      data: projects,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      status: "error",
      message: `Échec de la récupération : ${errorMessage}`,
      data: [],
    };
  }
}

// Génère un id numérique unique
function genId() {
  return Date.now() * 10000 + Math.floor(Math.random() * 10000);
}

// Fonction utilitaire pour valider une date au format YYYY-MM-DD
function isValidDateString(dateStr?: string): boolean {
  if (!dateStr) return false;
  // Vérifie le format de la date (YYYY-MM-DD)
  if (!/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return false;
  const [year, month, day] = dateStr.split("-").map(Number);
  if (!year || !month || !day) return false;
  // Mois entre 1 et 12, jour entre 1 et 31
  if (month < 1 || month > 12 || day < 1 || day > 31) return false;
  // Vérifie que la date est valide avec l'objet Date
  const d = new Date(dateStr);
  return !isNaN(d.getTime());
}

// Génère un hash SHA256 à partir d'une donnée (objet ou string)
function generateHash(data: object | string): string {
  const str = typeof data === "string" ? data : JSON.stringify(data);
  return CryptoJS.SHA256(str).toString(CryptoJS.enc.Hex);
}

// Crée ou récupère un terme dans la table terms
async function getOrCreateTermPrisma(tx: Prisma.TransactionClient, name: string, taxonomy?: string) {
  let term = await tx.terms.findFirst({ where: { name } });
  if (!term) {
    const termId = genId();
    // Tronquer le nom à 200 caractères max
    const safeName = name.slice(0, 200);
    term = await tx.terms.create({
      data: {
        term_id: termId,
        name: safeName,
        slug: slugify(safeName, { lower: true, strict: true }),
        term_group: 0,
      },
    });
    if (taxonomy) {
      await tx.termTaxonomy.create({
        data: {
          term_taxonomy_id: genId(),
          term_id: term.term_id,
          taxonomy,
          description: "",
          parent: 0,
          count: 0,
        },
      });
    }
  }
  return term;
}

export async function insertKoboProject(koboData: KoboProjectDTO) {
  // Log d'entrée du projet traité
  console.log("Tentative d'insertion du projet Kobo :", JSON.stringify(koboData, null, 2));

  // Validation
  const validation = validateKoboProject(koboData);
  if (!validation.success) {
    console.error("Validation échouée pour le projet :", JSON.stringify(koboData, null, 2));
    console.error("Détail de l'erreur de validation :", JSON.stringify(validation.error, null, 2));
    return { success: false, error: JSON.stringify(validation.error.issues) };
  }

  // Recherche d'un post existant par post_title et post_type
  const existingPost = await prisma.posts.findFirst({
    where: {
      post_title: koboData.title,
      post_type: 'project',
    },
  });
  if (existingPost) {
    console.log(`Projet déjà existant dans la base de données (post_title: ${koboData.title}, post_type: project, ID: ${existingPost.ID})`);
    return { success: false, error: 'Projet déjà existant dans la base de données', projectId: existingPost.ID };
  }

  const now = new Date();
  const postId = genId();
  const projectId = genId();
  const donationId = genId();

  // Dates Kobo sécurisées
  const safeStartDate = isValidDateString(koboData.start_date) ? new Date(koboData.start_date) : null;
  const safeEndDate = isValidDateString(koboData.end_date) ? new Date(koboData.end_date) : null;

  // Génération des hash pour les différentes parties du projet
  const infoHash = generateHash({
    title: koboData.title,
    description: koboData.description,
    type: koboData.type,
    status: koboData.status,
    progress: koboData.progress,
    start_date: koboData.start_date,
    end_date: koboData.end_date,
    target_population: koboData.target_population,
    budget: koboData.budget,
    mobilized: koboData.mobilized,
    disbursed: koboData.disbursed,
    sectors: koboData.sectors,
    sdgs: koboData.sdgs,
  });
  const effectsHash = generateHash(koboData.effects);
  const agenciesHash = generateHash(koboData.agencies);
  const zonesHash = generateHash({
    regions: koboData.regions,
    departments: koboData.departments,
    communes: koboData.communes,
  });
  const donationsHash = generateHash({
    donor: koboData.donor,
    budget: koboData.budget,
    effects: koboData.effects,
    sdgs: koboData.sdgs,
  });
  const fundingsHash = generateHash(koboData.agencies.map(a => ({
    name: a.name,
    received: a.received,
    disbursed: a.disbursed,
  })));

  try {
    return await prisma.$transaction(async (tx) => {
      // 1. Insertion dans posts
      const postSlug = slugify(koboData.title, { lower: true, strict: true }).slice(0, 200);
      await tx.posts.create({
        data: {
          ID: postId,
          post_author: 1,
          post_date: now,
          post_date_gmt: now,
          post_content: koboData.description,
          post_title: koboData.title,
          post_excerpt: "",
          post_status: "publish",
          comment_status: "closed",
          ping_status: "closed",
          post_password: "",
          post_name: postSlug,
          to_ping: "",
          pinged: "",
          post_modified: now,
          post_modified_gmt: now,
          post_content_filtered: "",
          post_parent: 0,
          guid: `https://illimi-niger.com/?post_type=project&p=${postId}`,
          menu_order: 0,
          post_type: "project",
          post_mime_type: "",
          comment_count: 0,
        },
      });

      // 2. Insertion dans postMeta (exemple pour raw_project_data)
      await tx.postMeta.create({
        data: {
          meta_id: genId(),
          post_id: postId,
          meta_key: "raw_project_data",
          meta_value: JSON.stringify(koboData),
        },
      });

      // 3. Insertion dans projects
      await tx.projects.create({
        data: {
          id: BigInt(projectId),
          post_id: postId,
          revision_id: 0,
          status: koboData.status,
          title: koboData.title,
          type: koboData.type,
          description: koboData.description,
          target_population: koboData.target_population || [],
          overall_budget: koboData.budget,
          info_hash: infoHash,
          info_revision: 1,
          extensions_hash: "",
          extensions_revision: 0,
          effects_hash: effectsHash,
          effects_revision: 0,
          agencies_hash: agenciesHash,
          agencies_revision: 0,
          zones_hash: zonesHash,
          zones_revision: 0,
          donations_hash: donationsHash,
          donations_revision: 0,
          fundings_hash: fundingsHash,
          fundings_revision: 0,
          created_at: now,
          updated_at: now,
        },
      });

      // 4. Agences et financements
      for (const agency of koboData.agencies || []) {
        const agencyTerm = await getOrCreateTermPrisma(tx, agency.name, "agency");
        await tx.projectAgencies.create({
          data: {
            id: genId(),
            project_id: BigInt(projectId),
            agency_id: agencyTerm.term_id,
            focal_point_id: null,
            focal_type: "normal",
            revision_id: 0,
            revision_author_id: 1,
            created_at: now,
            updated_at: now,
          },
        });
        await tx.projectFundings.create({
          data: {
            id: genId(),
            project_id: BigInt(projectId),
            agency_id: agencyTerm.term_id,
            received: agency.received,
            disbursed: agency.disbursed,
            revision_id: 0,
            revision_author_id: 1,
            created_at: now,
            updated_at: now,
          },
        });
      }

      // 5. Donateur et donation
      const donorTerm = await getOrCreateTermPrisma(tx, koboData.donor, "donor");
      await tx.projectDonations.create({
        data: {
          id: BigInt(donationId),
          project_id: BigInt(projectId),
          donor_id: donorTerm.term_id,
          amount: koboData.budget,
          effect_ids: koboData.effects,
          signature_date: safeStartDate || now,
          expiry_date: safeEndDate || now,
          revision_id: 0,
          revision_author_id: 1,
          created_at: now,
          updated_at: now,
        },
      });

      // 6. Budgets par SDG
      for (const sdg of koboData.sdgs || []) {
        const sdgTerm = await getOrCreateTermPrisma(tx, sdg, "sdg");
        await tx.projectDonationBudgets.create({
          data: {
            id: genId(),
            project_id: BigInt(projectId),
            donation_id: BigInt(donationId),
            sdg_id: sdgTerm.term_id,
            amount_disbursed: koboData.disbursed / (koboData.sdgs?.length || 1),
            amount_mobilized: koboData.mobilized / (koboData.sdgs?.length || 1),
            percentage_disbursed: 100,
            percentage_mobilized: 100,
            available_budget: 0,
            revision_id: 0,
            revision_author_id: 1,
            created_at: now,
            updated_at: now,
          },
        });
      }

      // 7. Project info
      await tx.projectInfo.create({
        data: {
          id: genId(),
          project_id: BigInt(projectId),
          status: koboData.status,
          progress: koboData.progress,
          sector_ids: koboData.sectors || [],
          start_date: safeStartDate || now,
          end_date: safeEndDate || now,
          revision_id: 0,
          revision_author_id: 1,
          created_at: now,
          updated_at: now,
        },
      });

      // 8. Zones
      for (let i = 0; i < (koboData.communes?.length || 0); i++) {
        const regionId = await getOrCreateTermPrisma(tx, koboData.regions[i % koboData.regions.length], "region");
        const departmentId = await getOrCreateTermPrisma(tx, koboData.departments[i % koboData.departments.length], "department");
        const communeId = await getOrCreateTermPrisma(tx, koboData.communes[i], "commune");
        await tx.projectZones.create({
          data: {
            id: genId(),
            project_id: BigInt(projectId),
            region_id: regionId.term_id,
            department_id: departmentId.term_id,
            commune_id: communeId.term_id,
            revision_id: 0,
            revision_author_id: 1,
            created_at: now,
            updated_at: now,
          },
        });
      }

      return { success: true, projectId };
    });
  } catch (error) {
    // Log détaillé en cas d'échec d'insertion
    console.error("Erreur lors de l'insertion du projet Kobo :", JSON.stringify(koboData, null, 2));
    console.error("Erreur Prisma ou JS :", error);
    return { success: false, error: (error as Error).message };
  }
}
