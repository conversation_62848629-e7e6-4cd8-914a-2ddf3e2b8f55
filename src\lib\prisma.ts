import { PrismaClient } from "@/generated/prisma";

let prisma: PrismaClient | null = null;

if (!process.env.DATABASE_URL) {
  console.error("Erreur : DATABASE_URL non défini dans .env");
} else {
  try {
    prisma = new PrismaClient();
  } catch (error) {
    console.error("Erreur lors de l'initialisation de PrismaClient :", error);
  }
}

if (!prisma) throw new Error("PrismaClient not initialized");

export async function testMySQLConnection() {
  if (!prisma) {
    return {
      status: "error",
      message:
        "PrismaClient non initialisé. Vérifiez DATABASE_URL et le serveur MySQL.",
    };
  }
  try {
    await prisma.$connect();
    return { status: "success", message: "Connexion MySQL réussie" };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    return {
      status: "error",
      message: `Échec de la connexion MySQL : ${errorMessage}`,
    };
  } finally {
    await prisma.$disconnect();
  }
}

export default prisma!;
