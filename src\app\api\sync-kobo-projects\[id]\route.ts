import { NextResponse } from "next/server";
import { fetchKoboProjectsAll, insertKoboProject } from "@/lib/kobo";

export async function POST(request: Request, { params }: { params: { id: string } }) {
  const { id } = params;
  try {
    // Récupérer tous les projets Kobo
    const { status, data } = await fetchKoboProjectsAll();
    if (status !== "success") {
      return NextResponse.json({ success: false, error: "Impossible de récupérer les projets Kobo" }, { status: 500 });
    }
    // Trouver le projet à relancer
    const project = data.find((p: any) => String(p.id) === id);
    if (!project) {
      return NextResponse.json({ success: false, error: "Projet non trouvé dans Kobo" }, { status: 404 });
    }
    // Relancer l'insertion
    const result = await insertKoboProject(project);
    return NextResponse.json({ ...result, id });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      id,
    }, { status: 500 });
  }
} 