/**
 * Database migration script to identify and fix invalid datetime values
 * Specifically targets MySQL invalid datetime values like '0000-00-00 00:00:00'
 */

import prisma from "../src/lib/prisma";
import { sanitizeDateTime, isInvalidDateTime } from "../src/lib/datetime-utils";

interface InvalidDatetimeRecord {
  table: string;
  id: string | number;
  field: string;
  currentValue: string;
  suggestedValue: string | null;
}

/**
 * Identifies invalid datetime values in the posts table
 */
async function findInvalidDatetimesInPosts(): Promise<InvalidDatetimeRecord[]> {
  console.log("🔍 Checking posts table for invalid datetime values...");
  
  const invalidRecords: InvalidDatetimeRecord[] = [];
  
  try {
    // Get all posts with raw SQL to see actual datetime values
    const posts = await prisma.$queryRaw<any[]>`
      SELECT ID, post_date, post_date_gmt, post_modified, post_modified_gmt 
      FROM lmczp_posts 
      WHERE post_date = '0000-00-00 00:00:00' 
         OR post_date_gmt = '0000-00-00 00:00:00'
         OR post_modified = '0000-00-00 00:00:00'
         OR post_modified_gmt = '0000-00-00 00:00:00'
         OR post_date LIKE '0000-%'
         OR post_date_gmt LIKE '0000-%'
         OR post_modified LIKE '0000-%'
         OR post_modified_gmt LIKE '0000-%'
         OR post_date LIKE '%-00-%'
         OR post_date_gmt LIKE '%-00-%'
         OR post_modified LIKE '%-00-%'
         OR post_modified_gmt LIKE '%-00-%'
    `;
    
    for (const post of posts) {
      const dateFields = ['post_date', 'post_date_gmt', 'post_modified', 'post_modified_gmt'];
      
      for (const field of dateFields) {
        const value = post[field];
        if (isInvalidDateTime(value)) {
          invalidRecords.push({
            table: 'lmczp_posts',
            id: post.ID,
            field,
            currentValue: value,
            suggestedValue: new Date().toISOString().slice(0, 19).replace('T', ' ')
          });
        }
      }
    }
    
    console.log(`Found ${invalidRecords.length} invalid datetime values in posts table`);
    return invalidRecords;
    
  } catch (error) {
    console.error("Error checking posts table:", error);
    return [];
  }
}

/**
 * Identifies invalid datetime values in other tables
 */
async function findInvalidDatetimesInOtherTables(): Promise<InvalidDatetimeRecord[]> {
  console.log("🔍 Checking other tables for invalid datetime values...");
  
  const invalidRecords: InvalidDatetimeRecord[] = [];
  
  try {
    // Check actionscheduler tables
    const actionSchedulerActions = await prisma.$queryRaw<any[]>`
      SELECT action_id, scheduled_date_gmt, scheduled_date_local, last_attempt_gmt, last_attempt_local
      FROM lmczp_actionscheduler_actions 
      WHERE scheduled_date_gmt = '0000-00-00 00:00:00' 
         OR scheduled_date_local = '0000-00-00 00:00:00'
         OR last_attempt_gmt = '0000-00-00 00:00:00'
         OR last_attempt_local = '0000-00-00 00:00:00'
    `;
    
    for (const record of actionSchedulerActions) {
      const dateFields = ['scheduled_date_gmt', 'scheduled_date_local', 'last_attempt_gmt', 'last_attempt_local'];
      
      for (const field of dateFields) {
        const value = record[field];
        if (isInvalidDateTime(value)) {
          invalidRecords.push({
            table: 'lmczp_actionscheduler_actions',
            id: record.action_id,
            field,
            currentValue: value,
            suggestedValue: null // These can be null
          });
        }
      }
    }
    
    // Check comments table
    const comments = await prisma.$queryRaw<any[]>`
      SELECT comment_ID, comment_date, comment_date_gmt
      FROM lmczp_comments 
      WHERE comment_date = '0000-00-00 00:00:00' 
         OR comment_date_gmt = '0000-00-00 00:00:00'
    `;
    
    for (const comment of comments) {
      const dateFields = ['comment_date', 'comment_date_gmt'];
      
      for (const field of dateFields) {
        const value = comment[field];
        if (isInvalidDateTime(value)) {
          invalidRecords.push({
            table: 'lmczp_comments',
            id: comment.comment_ID,
            field,
            currentValue: value,
            suggestedValue: new Date().toISOString().slice(0, 19).replace('T', ' ')
          });
        }
      }
    }
    
    console.log(`Found ${invalidRecords.length} invalid datetime values in other tables`);
    return invalidRecords;
    
  } catch (error) {
    console.error("Error checking other tables:", error);
    return [];
  }
}

/**
 * Fixes invalid datetime values in the posts table
 */
async function fixInvalidDatetimesInPosts(records: InvalidDatetimeRecord[]): Promise<void> {
  console.log("🔧 Fixing invalid datetime values in posts table...");
  
  const postsRecords = records.filter(r => r.table === 'lmczp_posts');
  
  for (const record of postsRecords) {
    try {
      const updateValue = record.suggestedValue || new Date().toISOString().slice(0, 19).replace('T', ' ');
      
      await prisma.$executeRaw`
        UPDATE lmczp_posts 
        SET ${prisma.Prisma.raw(record.field)} = ${updateValue}
        WHERE ID = ${record.id}
      `;
      
      console.log(`✅ Fixed ${record.table}.${record.field} for ID ${record.id}: ${record.currentValue} → ${updateValue}`);
      
    } catch (error) {
      console.error(`❌ Failed to fix ${record.table}.${record.field} for ID ${record.id}:`, error);
    }
  }
}

/**
 * Fixes invalid datetime values in other tables
 */
async function fixInvalidDatetimesInOtherTables(records: InvalidDatetimeRecord[]): Promise<void> {
  console.log("🔧 Fixing invalid datetime values in other tables...");
  
  const otherRecords = records.filter(r => r.table !== 'lmczp_posts');
  
  for (const record of otherRecords) {
    try {
      const updateValue = record.suggestedValue;
      
      if (updateValue === null) {
        // Set to NULL for fields that can be null
        await prisma.$executeRaw`
          UPDATE ${prisma.Prisma.raw(record.table)}
          SET ${prisma.Prisma.raw(record.field)} = NULL
          WHERE ${prisma.Prisma.raw(getIdField(record.table))} = ${record.id}
        `;
        
        console.log(`✅ Fixed ${record.table}.${record.field} for ID ${record.id}: ${record.currentValue} → NULL`);
      } else {
        await prisma.$executeRaw`
          UPDATE ${prisma.Prisma.raw(record.table)}
          SET ${prisma.Prisma.raw(record.field)} = ${updateValue}
          WHERE ${prisma.Prisma.raw(getIdField(record.table))} = ${record.id}
        `;
        
        console.log(`✅ Fixed ${record.table}.${record.field} for ID ${record.id}: ${record.currentValue} → ${updateValue}`);
      }
      
    } catch (error) {
      console.error(`❌ Failed to fix ${record.table}.${record.field} for ID ${record.id}:`, error);
    }
  }
}

/**
 * Gets the primary key field name for a table
 */
function getIdField(tableName: string): string {
  const idFields: Record<string, string> = {
    'lmczp_actionscheduler_actions': 'action_id',
    'lmczp_actionscheduler_claims': 'claim_id',
    'lmczp_actionscheduler_logs': 'log_id',
    'lmczp_comments': 'comment_ID',
    'lmczp_posts': 'ID'
  };
  
  return idFields[tableName] || 'id';
}

/**
 * Main function to run the migration
 */
async function main() {
  console.log("🚀 Starting invalid datetime migration...");
  
  try {
    // Find all invalid datetime values
    const postsRecords = await findInvalidDatetimesInPosts();
    const otherRecords = await findInvalidDatetimesInOtherTables();
    
    const allRecords = [...postsRecords, ...otherRecords];
    
    if (allRecords.length === 0) {
      console.log("✅ No invalid datetime values found!");
      return;
    }
    
    console.log(`\n📊 Summary of invalid datetime values found:`);
    console.log(`Total records: ${allRecords.length}`);
    
    // Group by table
    const byTable = allRecords.reduce((acc, record) => {
      acc[record.table] = (acc[record.table] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    Object.entries(byTable).forEach(([table, count]) => {
      console.log(`  ${table}: ${count} records`);
    });
    
    // Ask for confirmation (in a real scenario, you might want to add a CLI prompt)
    console.log("\n⚠️  This will modify your database. Make sure you have a backup!");
    
    // Fix the issues
    await fixInvalidDatetimesInPosts(postsRecords);
    await fixInvalidDatetimesInOtherTables(otherRecords);
    
    console.log("\n✅ Migration completed successfully!");
    
  } catch (error) {
    console.error("❌ Migration failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration if this file is executed directly
if (require.main === module) {
  main();
}

export { main as fixInvalidDatetimes };
