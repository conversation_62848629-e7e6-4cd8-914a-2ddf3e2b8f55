# Plateforme de Synchronisation Kobo → MySQL (Next.js, Prisma)

## 🚀 Présentation
Ce projet permet de synchroniser automatiquement des projets issus de KoboToolbox vers une base de données MySQL, avec une interface d’administration moderne (Next.js) pour piloter, visualiser et relancer la synchronisation.

---

## 🛠️ Prérequis
- **Node.js** v18+
- **npm** ou **yarn**
- **MySQL** (local ou distant)
- **Accès KoboToolbox** (token API)
- Variables d’environnement :
  - `DATABASE_URL` (connexion MySQL pour Prisma)
  - `KOBO_TOKEN` (token API Kobo)
  - `KOBO_ASSET_ID` (ID du formulaire Kobo)
  - (optionnel) `DB_HOST`, `DB_USER`, `DB_PASSWORD`, `DB_NAME` pour accès direct SQL

---

## ⚡ Installation
```bash
# 1. <PERSON><PERSON><PERSON> le repo
 git clone <repo-url>
 cd <repo>

# 2. Installer les dépendances
 npm install
# ou
yarn install

# 3. Configurer les variables d'environnement
cp .env.example .env
# (remplir les valeurs dans .env)

# 4. Générer le client Prisma
npx prisma generate

# 5. Appliquer les migrations (si besoin)
npx prisma migrate deploy
```

---

## ▶️ Lancement
```bash
# En développement
npm run dev
# ou
yarn dev

# En production
npm run build && npm start
```

---

## ✨ Fonctionnalités principales
- **Synchronisation Kobo → MySQL** (API sécurisée, rollback automatique)
- **Rapport détaillé** après chaque synchro (insérés, ignorés, erreurs, mis à jour)
- **Interface d’administration** (Next.js) :
  - Lancer la synchro
  - Voir le rapport détaillé (tableau, filtres)
  - Relancer individuellement un projet en erreur
- **Gestion des doublons** (aucun projet inséré deux fois)
- **Logs détaillés** pour chaque opération et chaque erreur
- **Validation stricte** des données Kobo (Zod)
- **Rollback transactionnel** (Prisma)

---

## 🔄 Schéma du flux

```mermaid
graph TD;
  Kobo[KoboToolbox API]
  API[API Next.js / Prisma]
  DB[(MySQL DB)]
  UI[Interface Admin Next.js]

  Kobo --> API
  API --> DB
  DB --> UI
  API --> UI
```

---

## 💡 Points forts techniques
- Transactions atomiques (rollback sur erreur)
- Gestion fine des doublons et des mises à jour
- Logs et rapport détaillés pour le debug
- Relance individuelle des projets en erreur
- Code modulaire, typé TypeScript, facile à maintenir

---

