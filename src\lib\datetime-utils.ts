/**
 * Utilities for handling and validating datetime values
 * Specifically designed to handle invalid MySQL datetime values like '0000-00-00 00:00:00'
 */

/**
 * Validates if a date string is in valid YYYY-MM-DD format
 */
export function isValidDateString(dateStr?: string): boolean {
  if (!dateStr) return false;
  // Vérifie le format de la date (YYYY-MM-DD)
  if (!/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) return false;
  const [year, month, day] = dateStr.split("-").map(Number);
  if (!year || !month || !day) return false;
  // Mois entre 1 et 12, jour entre 1 et 31
  if (month < 1 || month > 12 || day < 1 || day > 31) return false;
  // Vérifie que la date est valide avec l'objet Date
  const d = new Date(dateStr);
  return !isNaN(d.getTime());
}

/**
 * Checks if a datetime value is invalid (common MySQL invalid formats)
 */
export function isInvalidDateTime(dateValue?: Date | string | null): boolean {
  if (!dateValue) return true;
  
  if (typeof dateValue === 'string') {
    // Check for common invalid MySQL datetime formats
    const invalidPatterns = [
      '0000-00-00',
      '0000-00-00 00:00:00',
      /^0000-/,           // Year 0000
      /-00-/,             // Month 00
      /-00 /,             // Day 00 (with space)
      /\d{4}-\d{2}-00/,   // Day 00
      /\d{4}-00-\d{2}/,   // Month 00
    ];
    
    return invalidPatterns.some(pattern => {
      if (typeof pattern === 'string') {
        return dateValue === pattern;
      } else {
        return pattern.test(dateValue);
      }
    });
  }
  
  if (dateValue instanceof Date) {
    return isNaN(dateValue.getTime());
  }
  
  return false;
}

/**
 * Sanitizes and validates datetime values, returning null for invalid dates
 */
export function sanitizeDateTime(dateValue?: Date | string | null): Date | null {
  if (!dateValue) return null;
  
  // Check for invalid datetime patterns first
  if (isInvalidDateTime(dateValue)) {
    console.warn(`Invalid datetime detected and ignored: ${dateValue}`);
    return null;
  }
  
  let dateToCheck: Date;
  
  if (typeof dateValue === 'string') {
    dateToCheck = new Date(dateValue);
  } else {
    dateToCheck = dateValue;
  }
  
  // Verify the date is valid
  if (isNaN(dateToCheck.getTime())) {
    console.warn(`Invalid date detected and ignored: ${dateValue}`);
    return null;
  }
  
  // Check if year is reasonable (between 1900 and 2100)
  const year = dateToCheck.getFullYear();
  if (year < 1900 || year > 2100) {
    console.warn(`Date with invalid year detected and ignored: ${dateValue} (year: ${year})`);
    return null;
  }
  
  return dateToCheck;
}

/**
 * Creates a safe datetime with fallback for invalid values
 */
export function createSafeDateTime(dateValue?: Date | string | null, fallback?: Date): Date {
  const sanitized = sanitizeDateTime(dateValue);
  return sanitized || fallback || new Date();
}

/**
 * Validates datetime before database operations
 * Throws an error if the datetime is invalid and no fallback is provided
 */
export function validateDateTimeForDB(
  dateValue?: Date | string | null, 
  fieldName?: string,
  allowNull: boolean = true
): Date | null {
  if (!dateValue) {
    if (allowNull) {
      return null;
    } else {
      throw new Error(`${fieldName || 'DateTime field'} cannot be null`);
    }
  }
  
  const sanitized = sanitizeDateTime(dateValue);
  if (!sanitized) {
    throw new Error(
      `Invalid datetime value for ${fieldName || 'field'}: ${dateValue}. ` +
      'Please provide a valid datetime or null.'
    );
  }
  
  return sanitized;
}

/**
 * Safely converts any datetime value to ISO string, handling invalid values
 */
export function toSafeISOString(dateValue?: Date | string | null): string | null {
  const sanitized = sanitizeDateTime(dateValue);
  return sanitized ? sanitized.toISOString() : null;
}

/**
 * Formats datetime for MySQL insertion, handling invalid values
 */
export function formatForMySQL(dateValue?: Date | string | null): string | null {
  const sanitized = sanitizeDateTime(dateValue);
  if (!sanitized) return null;
  
  // Format as YYYY-MM-DD HH:mm:ss for MySQL
  return sanitized.toISOString().slice(0, 19).replace('T', ' ');
}

/**
 * Batch sanitize multiple datetime fields in an object
 */
export function sanitizeDateTimeFields<T extends Record<string, any>>(
  obj: T,
  dateFields: (keyof T)[]
): T {
  const sanitized = { ...obj };
  
  dateFields.forEach(field => {
    if (sanitized[field] !== undefined) {
      sanitized[field] = sanitizeDateTime(sanitized[field] as any) as T[keyof T];
    }
  });
  
  return sanitized;
}
