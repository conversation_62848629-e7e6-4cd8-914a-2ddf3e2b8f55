// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}


model Posts {
  ID                Int      @id @default(autoincrement())
  post_author       Int
  post_date         DateTime @default(now())
  post_date_gmt     DateTime @default(now())
  post_content      String   @db.LongText
  post_title        String
  post_excerpt      String   @default("")
  post_status       String   @default("publish")
  comment_status    String   @default("closed")
  ping_status       String   @default("closed")
  post_password     String   @default("")
  post_name         String   @default("")
  to_ping           String   @default("")
  pinged            String   @default("")
  post_modified     DateTime @default(now())
  post_modified_gmt DateTime @default(now())
  post_content_filtered String @db.LongText @default("")
  post_parent       Int      @default(0)
  guid              String   @default("")
  menu_order        Int      @default(0)
  post_type         String   @default("project")
  post_mime_type    String   @default("")
  comment_count     Int      @default(0)
  postmeta          PostMeta[]
  projects          Projects[]
  term_relationships TermRelationships[]
  author            Users    @relation(fields: [post_author], references: [ID])

  @@map("lmczp_posts")
}

model PostMeta {
  meta_id    BigInt      @id
  post_id    Int
  meta_key   String
  meta_value String?  @db.LongText
  post       Posts    @relation(fields: [post_id], references: [ID])

  @@map("lmczp_postmeta")
}

model Projects {
  id                BigInt   @id
  post_id           Int
  revision_id       Int      @default(0)
  status            String   @default("pending")
  title             String
  type              String
  description       String
  target_population Json
  overall_budget    Float
  info_hash         String?
  info_revision     Int?     @default(0)
  extensions_hash   String?
  extensions_revision Int?   @default(0)
  effects_hash      String?
  effects_revision  Int?     @default(0)
  agencies_hash     String?
  agencies_revision Int?     @default(0)
  zones_hash        String?
  zones_revision    Int?     @default(0)
  donations_hash    String?
  donations_revision Int?    @default(0)
  fundings_hash     String?
  fundings_revision Int?     @default(0)
  created_at        DateTime?
  updated_at        DateTime?
  post              Posts    @relation(fields: [post_id], references: [ID])
  info              ProjectInfo[]
  agencies          ProjectAgencies[]
  zones             ProjectZones[]
  donations         ProjectDonations[]
  fundings          ProjectFundings[]
  effects           ProjectEffects[]
  extensions        ProjectExtensions[] @relation("ProjectToProjectExtensions")
  donation_budgets  ProjectDonationBudgets[]

  @@map("lmczp_pacad_projects")
}

model ProjectInfo {
  id                BigInt   @id
  project_id        BigInt
  status            String
  progress          String
  sector_ids        Json?
  start_date        DateTime
  end_date          DateTime
  revision_id       Int      @default(0)
  revision_author_id Int
  created_at        DateTime?
  updated_at        DateTime?
  project           Projects @relation(fields: [project_id], references: [id])
  revision_author   Users?   @relation(fields: [revision_author_id], references: [ID])

  @@map("lmczp_pacad_project_info")
}

model ProjectAgencies {
  id                BigInt   @id
  project_id        BigInt
  agency_id         Int
  focal_point_id    Int?
  focal_type        String
  revision_id       Int      @default(0)
  revision_author_id Int
  created_at        DateTime?
  updated_at        DateTime?
  project           Projects @relation(fields: [project_id], references: [id])
  agency            Terms    @relation(fields: [agency_id], references: [term_id])
  focal_point       Users?   @relation("FocalPoint", fields: [focal_point_id], references: [ID])
  revision_author   Users?   @relation("RevisionAuthorProjectAgencies", fields: [revision_author_id], references: [ID])

  @@map("lmczp_pacad_project_agencies")
}

model ProjectDonations {
  id                BigInt   @id
  project_id        BigInt
  donor_id          Int
  amount            Float
  effect_ids        Json
  signature_date    DateTime
  expiry_date       DateTime
  extensions_hash   String?
  extensions_revision Int?
  budgets_hash      String?
  budgets_revision  Int?
  revision_id       Int      @default(0)
  revision_author_id Int
  created_at        DateTime?
  updated_at        DateTime?
  project           Projects @relation(fields: [project_id], references: [id])
  donor             Terms    @relation(fields: [donor_id], references: [term_id])
  budgets           ProjectDonationBudgets[]
  revision_author   Users?   @relation(fields: [revision_author_id], references: [ID])
  extensions        ProjectExtensions[] @relation("ProjectDonationsToProjectExtensions")

  @@map("lmczp_pacad_project_donations")
}

model ProjectDonationBudgets {
  id                BigInt   @id
  project_id        BigInt
  donation_id       BigInt
  sdg_id            BigInt
  amount_disbursed  Float    @default(0.0)
  amount_mobilized  Float    @default(0.0)
  percentage_disbursed Float @default(0.0)
  percentage_mobilized Float @default(0.0)
  available_budget   Float    @default(0.0)
  diff_amount_disbursed Float?
  diff_amount_mobilized Float?
  diff_percentage_disbursed Float?
  diff_percentage_mobilized Float?
  diff_available_budget Float?
  revision_id        Int      @default(0)
  revision_author_id Int
  created_at        DateTime?
  updated_at        DateTime?
  project           Projects @relation(fields: [project_id], references: [id])
  donation          ProjectDonations @relation(fields: [donation_id], references: [id])
  revision_author   Users?   @relation(fields: [revision_author_id], references: [ID])

  @@map("lmczp_pacad_project_donation_budgets")
}

model ProjectEffects {
  id                BigInt   @id
  project_id        BigInt
  effect_id         Int
  product_id        Int
  data              Json
  sdg_ids           Json
  pdes_ids          Json
  uniss_ids         Json
  revision_id       Int      @default(0)
  revision_author_id Int
  created_at        DateTime?
  updated_at        DateTime?
  project           Projects @relation(fields: [project_id], references: [id])
  revision_author   Users?   @relation(fields: [revision_author_id], references: [ID])

  @@map("lmczp_pacad_project_effects")
}

model ProjectFundings {
  id                BigInt   @id
  project_id        BigInt
  agency_id         Int
  received          Float
  disbursed         Float
  diff_received     Float?
  diff_disbursed    Float?
  revision_id       Int      @default(0)
  revision_author_id Int
  created_at        DateTime?
  updated_at        DateTime?
  project           Projects @relation(fields: [project_id], references: [id])
  agency            Terms    @relation(fields: [agency_id], references: [term_id])
  revision_author   Users?   @relation(fields: [revision_author_id], references: [ID])

  @@map("lmczp_pacad_project_fundings")
}

model ProjectExtensions {
  id                BigInt   @id
  project_id        BigInt
  donation_id       BigInt?
  extension_date    DateTime
  revision_id       Int      @default(0)
  revision_author_id Int
  created_at        DateTime?
  updated_at        DateTime?
  project           Projects @relation("ProjectToProjectExtensions", fields: [project_id], references: [id])
  donation          ProjectDonations? @relation("ProjectDonationsToProjectExtensions", fields: [donation_id], references: [id])
  revision_author   Users?   @relation(fields: [revision_author_id], references: [ID])

  @@map("lmczp_pacad_project_extensions")
}

model ProjectZones {
  id                BigInt   @id
  project_id        BigInt
  region_id         Int
  department_id     Int?
  commune_id        Int?
  revision_id       Int      @default(0)
  revision_author_id Int
  created_at        DateTime?
  updated_at        DateTime?
  project           Projects @relation(fields: [project_id], references: [id])
  region            Terms    @relation("Region", fields: [region_id], references: [term_id])
  department        Terms?   @relation("Department", fields: [department_id], references: [term_id])
  commune           Terms?   @relation("Commune", fields: [commune_id], references: [term_id])
  revision_author   Users?   @relation(fields: [revision_author_id], references: [ID])

  @@map("lmczp_pacad_project_zones")
}

model Terms {
  term_id          Int      @id @default(autoincrement())
  name             String
  slug             String
  term_group       Int      @default(0)
  term_taxonomy    TermTaxonomy[]
  agencies         ProjectAgencies[]
  donations        ProjectDonations[]
  fundings         ProjectFundings[]
  zones_region     ProjectZones[]   @relation("Region")
  zones_department ProjectZones[]   @relation("Department")
  zones_commune    ProjectZones[]   @relation("Commune")

  @@map("lmczp_terms")
}

model TermTaxonomy {
  term_taxonomy_id Int      @id @default(autoincrement())
  term_id          Int
  taxonomy         String
  description      String   @default("")
  parent           Int      @default(0)
  count            Int      @default(0)
  term             Terms    @relation(fields: [term_id], references: [term_id])
  term_relationships TermRelationships[]

  @@map("lmczp_term_taxonomy")
}

model TermRelationships {
  object_id        Int
  term_taxonomy_id Int
  term_order       Int      @default(0)
  post             Posts    @relation(fields: [object_id], references: [ID])
  term_taxonomy    TermTaxonomy @relation(fields: [term_taxonomy_id], references: [term_taxonomy_id])

  @@id([object_id, term_taxonomy_id])
  @@map("lmczp_term_relationships")
}

model Users {
  ID           Int      @id @default(autoincrement())
  user_login   String
  user_pass    String   @default("")
  user_nicename String
  user_email   String
  user_url     String   @default("")
  user_registered DateTime @default(now())
  user_activation_key String @default("")
  user_status  Int      @default(0)
  display_name String
  posts        Posts[]
  agencies     ProjectAgencies[]        @relation("FocalPoint")
  revision_author_project_agencies ProjectAgencies[] @relation("RevisionAuthorProjectAgencies")
  project_info ProjectInfo[]
  project_donations ProjectDonations[]
  project_budgets ProjectDonationBudgets[]
  project_effects ProjectEffects[]
  project_fundings ProjectFundings[]
  project_extensions ProjectExtensions[]
  project_zones   ProjectZones[]

  @@map("lmczp_users")
}