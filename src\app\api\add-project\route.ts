// import { NextResponse } from "next/server";
// import { createProjectWithTransaction } from "@/lib/prisma";

// export async function POST(request: Request) {
//   try {
//     const body = await request.json();
//     const project = await createProjectWithTransaction(body);

//     console.log("Projet ajouté avec succès :", project.project.id.toString());
//     return NextResponse.json({ success: true, project: { ...project.project, id: project.project.id.toString() } });
//   } catch (error) {
//     if (error instanceof Error) {
//       console.error("Erreur dans /api/add-project :", error.message);
//       return NextResponse.json(
//         { success: false, error: error.message },
//         { status: 500 }
//       );
//     } else {
//       console.error("Erreur dans /api/add-project :", error);
//       return NextResponse.json(
//         { success: false, error: String(error) },
//         { status: 500 }
//       );
//     }
//   }
// }
