/**
 * Safe Prisma operations wrapper to handle invalid datetime values
 */

import prisma from "./prisma";
import { sanitizeDateTime, validateDateTimeForDB, sanitizeDateTimeFields } from "./datetime-utils";

/**
 * Safe wrapper for Prisma findFirst operations that handles datetime validation errors
 */
export async function safeFindFirst<T>(
  model: any,
  args: any,
  options?: { 
    skipDateValidation?: boolean;
    dateFields?: string[];
  }
): Promise<T | null> {
  try {
    return await model.findFirst(args);
  } catch (error: any) {
    // Check if it's the specific datetime error we're trying to fix
    if (error.message && error.message.includes('post_modified') && 
        error.message.includes('invalid datetime value')) {
      
      console.warn("Detected invalid datetime error, attempting to fix...");
      
      // If we have date fields specified, try to sanitize the where clause
      if (options?.dateFields && args.where) {
        const sanitizedWhere = sanitizeDateTimeFields(args.where, options.dateFields);
        
        try {
          return await model.findFirst({
            ...args,
            where: sanitizedWhere
          });
        } catch (retryError) {
          console.error("Retry with sanitized dates also failed:", retryError);
          throw retryError;
        }
      }
      
      // If no date fields specified or sanitization didn't work, throw original error
      throw error;
    }
    
    throw error;
  }
}

/**
 * Safe wrapper for Prisma findMany operations
 */
export async function safeFindMany<T>(
  model: any,
  args: any,
  options?: { 
    skipDateValidation?: boolean;
    dateFields?: string[];
  }
): Promise<T[]> {
  try {
    return await model.findMany(args);
  } catch (error: any) {
    if (error.message && error.message.includes('invalid datetime value')) {
      console.warn("Detected invalid datetime error in findMany, attempting to fix...");
      
      if (options?.dateFields && args.where) {
        const sanitizedWhere = sanitizeDateTimeFields(args.where, options.dateFields);
        
        try {
          return await model.findMany({
            ...args,
            where: sanitizedWhere
          });
        } catch (retryError) {
          console.error("Retry with sanitized dates also failed:", retryError);
          throw retryError;
        }
      }
    }
    
    throw error;
  }
}

/**
 * Safe wrapper for Prisma create operations with datetime validation
 */
export async function safeCreate<T>(
  model: any,
  args: any,
  options?: { 
    dateFields?: string[];
  }
): Promise<T> {
  try {
    // Sanitize datetime fields before creation
    if (options?.dateFields && args.data) {
      args.data = sanitizeDateTimeFields(args.data, options.dateFields);
    }
    
    return await model.create(args);
  } catch (error: any) {
    if (error.message && error.message.includes('invalid datetime value')) {
      console.error("Invalid datetime value in create operation:", error.message);
      console.error("Data being inserted:", JSON.stringify(args.data, null, 2));
    }
    
    throw error;
  }
}

/**
 * Safe wrapper for Prisma update operations with datetime validation
 */
export async function safeUpdate<T>(
  model: any,
  args: any,
  options?: { 
    dateFields?: string[];
  }
): Promise<T> {
  try {
    // Sanitize datetime fields before update
    if (options?.dateFields && args.data) {
      args.data = sanitizeDateTimeFields(args.data, options.dateFields);
    }
    
    return await model.update(args);
  } catch (error: any) {
    if (error.message && error.message.includes('invalid datetime value')) {
      console.error("Invalid datetime value in update operation:", error.message);
      console.error("Data being updated:", JSON.stringify(args.data, null, 2));
    }
    
    throw error;
  }
}

/**
 * Enhanced error handler for Prisma operations
 */
export function handlePrismaDateTimeError(error: any, operation: string, data?: any): never {
  if (error.message && error.message.includes('invalid datetime value')) {
    const enhancedMessage = `
Database datetime validation error in ${operation}:
${error.message}

This error typically occurs when trying to insert/query datetime values like:
- '0000-00-00 00:00:00'
- Dates with month or day set to zero
- Invalid date formats

Suggested solutions:
1. Run the datetime migration script: npm run fix-datetimes
2. Check your data for invalid datetime values
3. Ensure all datetime fields are properly validated before database operations

${data ? `Data involved: ${JSON.stringify(data, null, 2)}` : ''}
    `.trim();
    
    throw new Error(enhancedMessage);
  }
  
  throw error;
}

/**
 * Safe Posts operations with built-in datetime field handling
 */
export const safePosts = {
  findFirst: (args: any) => safeFindFirst(
    prisma.posts, 
    args, 
    { dateFields: ['post_date', 'post_date_gmt', 'post_modified', 'post_modified_gmt'] }
  ),
  
  findMany: (args: any) => safeFindMany(
    prisma.posts, 
    args, 
    { dateFields: ['post_date', 'post_date_gmt', 'post_modified', 'post_modified_gmt'] }
  ),
  
  create: (args: any) => safeCreate(
    prisma.posts, 
    args, 
    { dateFields: ['post_date', 'post_date_gmt', 'post_modified', 'post_modified_gmt'] }
  ),
  
  update: (args: any) => safeUpdate(
    prisma.posts, 
    args, 
    { dateFields: ['post_date', 'post_date_gmt', 'post_modified', 'post_modified_gmt'] }
  )
};

/**
 * Safe Projects operations
 */
export const safeProjects = {
  findFirst: (args: any) => safeFindFirst(
    prisma.projects, 
    args, 
    { dateFields: ['created_at', 'updated_at'] }
  ),
  
  findMany: (args: any) => safeFindMany(
    prisma.projects, 
    args, 
    { dateFields: ['created_at', 'updated_at'] }
  ),
  
  create: (args: any) => safeCreate(
    prisma.projects, 
    args, 
    { dateFields: ['created_at', 'updated_at'] }
  ),
  
  update: (args: any) => safeUpdate(
    prisma.projects, 
    args, 
    { dateFields: ['created_at', 'updated_at'] }
  )
};

/**
 * Safe ProjectInfo operations
 */
export const safeProjectInfo = {
  create: (args: any) => safeCreate(
    prisma.projectInfo, 
    args, 
    { dateFields: ['start_date', 'end_date', 'created_at', 'updated_at'] }
  ),
  
  update: (args: any) => safeUpdate(
    prisma.projectInfo, 
    args, 
    { dateFields: ['start_date', 'end_date', 'created_at', 'updated_at'] }
  )
};
