-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.9.6
-- https://www.phpmyadmin.net/
--
-- Hôte : z68yi.myd.infomaniak.com
-- G<PERSON><PERSON><PERSON> le :  mer. 11 juin 2025 à 12:47
-- Version du serveur :  10.4.17-MariaDB-1:10.4.17+maria~jessie-log
-- Version de PHP :  7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données :  `z68yi_illimi`
--

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_actionscheduler_actions`
--

CREATE TABLE `lmczp_actionscheduler_actions` (
  `action_id` bigint(20) UNSIGNED NOT NULL,
  `hook` varchar(191) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `status` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `scheduled_date_gmt` datetime DEFAULT '0000-00-00 00:00:00',
  `scheduled_date_local` datetime DEFAULT '0000-00-00 00:00:00',
  `args` varchar(191) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `schedule` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `group_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `attempts` int(11) NOT NULL DEFAULT 0,
  `last_attempt_gmt` datetime DEFAULT '0000-00-00 00:00:00',
  `last_attempt_local` datetime DEFAULT '0000-00-00 00:00:00',
  `claim_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `extended_args` varchar(8000) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `priority` tinyint(3) UNSIGNED NOT NULL DEFAULT 10
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;



-- --------------------------------------------------------

--
-- Structure de la table `lmczp_actionscheduler_claims`
--

CREATE TABLE `lmczp_actionscheduler_claims` (
  `claim_id` bigint(20) UNSIGNED NOT NULL,
  `date_created_gmt` datetime DEFAULT '0000-00-00 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_actionscheduler_groups`
--

CREATE TABLE `lmczp_actionscheduler_groups` (
  `group_id` bigint(20) UNSIGNED NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_actionscheduler_logs`
--

CREATE TABLE `lmczp_actionscheduler_logs` (
  `log_id` bigint(20) UNSIGNED NOT NULL,
  `action_id` bigint(20) UNSIGNED NOT NULL,
  `message` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `log_date_gmt` datetime DEFAULT '0000-00-00 00:00:00',
  `log_date_local` datetime DEFAULT '0000-00-00 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_ac_segments`
--

CREATE TABLE `lmczp_ac_segments` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `list_screen_id` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_id` bigint(20) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `url_parameters` mediumtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `global` tinyint(1) DEFAULT NULL,
  `date_created` datetime NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_admin_columns`
--

CREATE TABLE `lmczp_admin_columns` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `list_id` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `list_key` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `title` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `columns` mediumtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `settings` mediumtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `date_created` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `date_modified` datetime NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_aiowps_audit_log`
--

CREATE TABLE `lmczp_aiowps_audit_log` (
  `id` bigint(20) NOT NULL,
  `network_id` bigint(20) NOT NULL DEFAULT 0,
  `site_id` bigint(20) NOT NULL DEFAULT 0,
  `username` varchar(60) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `ip` varchar(45) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `level` varchar(25) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `event_type` varchar(25) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `details` text COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `stacktrace` text COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `created` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;



-- --------------------------------------------------------

--
-- Structure de la table `lmczp_aiowps_debug_log`
--

CREATE TABLE `lmczp_aiowps_debug_log` (
  `id` bigint(20) NOT NULL,
  `level` varchar(25) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `message` text COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `type` varchar(25) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `created` datetime NOT NULL DEFAULT '1000-10-10 10:00:00',
  `logtime` int(10) UNSIGNED DEFAULT NULL,
  `network_id` bigint(20) NOT NULL DEFAULT 0,
  `site_id` bigint(20) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_aiowps_events`
--

CREATE TABLE `lmczp_aiowps_events` (
  `id` bigint(20) NOT NULL,
  `event_type` varchar(150) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `username` varchar(150) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `event_date` datetime NOT NULL DEFAULT '1000-10-10 10:00:00',
  `ip_or_host` varchar(100) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `referer_info` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `country_code` varchar(50) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `event_data` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `created` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_aiowps_global_meta`
--

CREATE TABLE `lmczp_aiowps_global_meta` (
  `meta_id` bigint(20) NOT NULL,
  `date_time` datetime NOT NULL DEFAULT '1000-10-10 10:00:00',
  `meta_key1` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `meta_key2` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `meta_key3` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `meta_key4` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `meta_key5` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `meta_value1` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `meta_value2` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `meta_value3` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `meta_value4` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `meta_value5` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `created` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_aiowps_logged_in_users`
--

CREATE TABLE `lmczp_aiowps_logged_in_users` (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `username` varchar(60) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `site_id` bigint(20) NOT NULL,
  `created` int(10) UNSIGNED DEFAULT NULL,
  `expires` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_aiowps_login_lockdown`
--

CREATE TABLE `lmczp_aiowps_login_lockdown` (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `user_login` varchar(150) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `lockdown_date` datetime NOT NULL DEFAULT '1000-10-10 10:00:00',
  `release_date` datetime NOT NULL DEFAULT '1000-10-10 10:00:00',
  `failed_login_ip` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `lock_reason` varchar(128) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `unlock_key` varchar(128) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `is_lockout_email_sent` tinyint(1) NOT NULL DEFAULT 1,
  `backtrace_log` text COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `created` int(10) UNSIGNED DEFAULT NULL,
  `released` int(10) UNSIGNED DEFAULT NULL,
  `ip_lookup_result` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_aiowps_message_store`
--

CREATE TABLE `lmczp_aiowps_message_store` (
  `id` bigint(20) NOT NULL,
  `message_key` text COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `message_value` text COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `created` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_aiowps_permanent_block`
--

CREATE TABLE `lmczp_aiowps_permanent_block` (
  `id` bigint(20) NOT NULL,
  `blocked_ip` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `block_reason` varchar(128) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `country_origin` varchar(50) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `blocked_date` datetime NOT NULL DEFAULT '1000-10-10 10:00:00',
  `unblock` tinyint(1) NOT NULL DEFAULT 0,
  `created` int(10) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;



-- --------------------------------------------------------

--
-- Structure de la table `lmczp_commentmeta`
--

CREATE TABLE `lmczp_commentmeta` (
  `meta_id` bigint(20) UNSIGNED NOT NULL,
  `comment_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_comments`
--

CREATE TABLE `lmczp_comments` (
  `comment_ID` bigint(20) UNSIGNED NOT NULL,
  `comment_post_ID` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `comment_author` tinytext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `comment_author_email` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_author_url` varchar(200) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_author_IP` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `comment_date_gmt` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `comment_content` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `comment_karma` int(11) NOT NULL DEFAULT 0,
  `comment_approved` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '1',
  `comment_agent` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `comment_type` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'comment',
  `comment_parent` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_e_events`
--

CREATE TABLE `lmczp_e_events` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `event_data` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_jet_post_types`
--

CREATE TABLE `lmczp_jet_post_types` (
  `id` bigint(20) NOT NULL,
  `slug` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `status` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `labels` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `args` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_fields` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_jet_taxonomies`
--

CREATE TABLE `lmczp_jet_taxonomies` (
  `id` bigint(20) NOT NULL,
  `slug` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `object_type` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `status` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `labels` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `args` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_fields` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_links`
--

CREATE TABLE `lmczp_links` (
  `link_id` bigint(20) UNSIGNED NOT NULL,
  `link_url` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_name` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_image` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_target` varchar(25) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_description` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_visible` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'Y',
  `link_owner` bigint(20) UNSIGNED NOT NULL DEFAULT 1,
  `link_rating` int(11) NOT NULL DEFAULT 0,
  `link_updated` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `link_rel` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `link_notes` mediumtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `link_rss` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_maps`
--

CREATE TABLE `lmczp_mapsvg6_maps` (
  `id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT '',
  `options` longtext DEFAULT NULL,
  `svgFilePath` varchar(500) DEFAULT NULL,
  `svgFileLastChanged` int(10) UNSIGNED DEFAULT NULL,
  `version` varchar(20) DEFAULT NULL,
  `status` tinyint(3) UNSIGNED DEFAULT 1,
  `statusChangedAt` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_objects_0`
--

CREATE TABLE `lmczp_mapsvg6_objects_0` (
  `id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `location` text DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text DEFAULT NULL,
  `location_img` varchar(255) DEFAULT NULL,
  `regions` text DEFAULT NULL,
  `category` text DEFAULT NULL,
  `category_text` varchar(255) DEFAULT NULL,
  `images` text DEFAULT NULL,
  `link` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_objects_1`
--

CREATE TABLE `lmczp_mapsvg6_objects_1` (
  `id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `location` text DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text DEFAULT NULL,
  `location_img` varchar(255) DEFAULT NULL,
  `regions` text DEFAULT NULL,
  `category` text DEFAULT NULL,
  `category_text` varchar(255) DEFAULT NULL,
  `images` text DEFAULT NULL,
  `link` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_objects_2`
--

CREATE TABLE `lmczp_mapsvg6_objects_2` (
  `id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `location` text DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text DEFAULT NULL,
  `location_img` varchar(255) DEFAULT NULL,
  `regions` text DEFAULT NULL,
  `category` text DEFAULT NULL,
  `category_text` varchar(255) DEFAULT NULL,
  `images` text DEFAULT NULL,
  `link` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_objects_3`
--

CREATE TABLE `lmczp_mapsvg6_objects_3` (
  `id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `location` text DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text DEFAULT NULL,
  `location_img` varchar(255) DEFAULT NULL,
  `regions` text DEFAULT NULL,
  `category` text DEFAULT NULL,
  `category_text` varchar(255) DEFAULT NULL,
  `images` text DEFAULT NULL,
  `link` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_objects_4`
--

CREATE TABLE `lmczp_mapsvg6_objects_4` (
  `id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `location` text DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text DEFAULT NULL,
  `location_img` varchar(255) DEFAULT NULL,
  `regions` text DEFAULT NULL,
  `category` text DEFAULT NULL,
  `category_text` varchar(255) DEFAULT NULL,
  `images` text DEFAULT NULL,
  `link` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_r2o`
--

CREATE TABLE `lmczp_mapsvg6_r2o` (
  `objects_table` varchar(100) DEFAULT NULL,
  `regions_table` varchar(100) DEFAULT NULL,
  `region_id` varchar(100) DEFAULT NULL,
  `object_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_regions_0`
--

CREATE TABLE `lmczp_mapsvg6_regions_0` (
  `id` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) DEFAULT 'Enabled',
  `link` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_regions_1`
--

CREATE TABLE `lmczp_mapsvg6_regions_1` (
  `id` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) DEFAULT 'Enabled',
  `link` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_regions_2`
--

CREATE TABLE `lmczp_mapsvg6_regions_2` (
  `id` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) DEFAULT 'Enabled',
  `link` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_regions_3`
--

CREATE TABLE `lmczp_mapsvg6_regions_3` (
  `id` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) DEFAULT 'Enabled',
  `link` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_regions_4`
--

CREATE TABLE `lmczp_mapsvg6_regions_4` (
  `id` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) DEFAULT 'Enabled',
  `link` varchar(255) DEFAULT NULL,
  `population` varchar(255) DEFAULT NULL,
  `densite` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_regions_5`
--

CREATE TABLE `lmczp_mapsvg6_regions_5` (
  `id` varchar(255) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `status_text` varchar(255) DEFAULT NULL,
  `link` varchar(255) DEFAULT NULL,
  `population` varchar(255) DEFAULT NULL,
  `densite` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_schema`
--

CREATE TABLE `lmczp_mapsvg6_schema` (
  `id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT '',
  `name` varchar(255) DEFAULT '',
  `fields` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mapsvg6_settings`
--

CREATE TABLE `lmczp_mapsvg6_settings` (
  `key` varchar(100) NOT NULL,
  `value` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mec_attendees`
--

CREATE TABLE `lmczp_mec_attendees` (
  `attendee_id` bigint(20) NOT NULL,
  `post_id` bigint(20) NOT NULL,
  `event_id` bigint(20) NOT NULL,
  `occurrence` int(11) NOT NULL,
  `email` varchar(50) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `first_name` varchar(50) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `last_name` varchar(50) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `data` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `count` int(11) DEFAULT 1,
  `verification` int(11) DEFAULT 0,
  `confirmation` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mec_bookings`
--

CREATE TABLE `lmczp_mec_bookings` (
  `id` int(10) UNSIGNED NOT NULL,
  `booking_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED DEFAULT NULL,
  `transaction_id` varchar(20) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `event_id` int(10) UNSIGNED NOT NULL,
  `ticket_ids` varchar(655) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `seats` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `status` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'pending',
  `confirmed` tinyint(4) NOT NULL DEFAULT 0,
  `verified` tinyint(4) NOT NULL DEFAULT 0,
  `all_occurrences` tinyint(4) NOT NULL DEFAULT 0,
  `date` datetime NOT NULL,
  `timestamp` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mec_dates`
--

CREATE TABLE `lmczp_mec_dates` (
  `id` int(10) UNSIGNED NOT NULL,
  `post_id` int(11) NOT NULL,
  `dstart` date NOT NULL,
  `dend` date NOT NULL,
  `tstart` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `tend` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `status` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'publish',
  `public` int(10) UNSIGNED NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mec_events`
--

CREATE TABLE `lmczp_mec_events` (
  `id` int(11) NOT NULL,
  `post_id` int(11) NOT NULL,
  `start` date NOT NULL,
  `end` date NOT NULL,
  `repeat` tinyint(4) NOT NULL DEFAULT 0,
  `rinterval` varchar(10) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `year` varchar(80) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `month` varchar(80) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `day` varchar(80) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `week` varchar(80) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `weekday` varchar(80) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `weekdays` varchar(80) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `days` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `not_in_days` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `time_start` int(11) NOT NULL DEFAULT 0,
  `time_end` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mec_occurrences`
--

CREATE TABLE `lmczp_mec_occurrences` (
  `id` int(10) UNSIGNED NOT NULL,
  `post_id` int(10) UNSIGNED NOT NULL,
  `occurrence` int(10) UNSIGNED NOT NULL,
  `params` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_mec_users`
--

CREATE TABLE `lmczp_mec_users` (
  `id` int(11) NOT NULL,
  `first_name` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `last_name` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `email` varchar(127) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `reg` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_options`
--

CREATE TABLE `lmczp_options` (
  `option_id` bigint(20) UNSIGNED NOT NULL,
  `option_name` varchar(191) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `option_value` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `autoload` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'yes'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;



-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_documents`
--

CREATE TABLE `lmczp_pacad_documents` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `post_id` bigint(20) UNSIGNED NOT NULL,
  `event_id` bigint(20) UNSIGNED DEFAULT NULL,
  `committee_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `author` varchar(500) NOT NULL,
  `language` varchar(255) DEFAULT NULL,
  `publication_year` int(11) DEFAULT NULL,
  `attachment_id` int(11) NOT NULL,
  `mime_type` varchar(255) DEFAULT NULL,
  `status` varchar(255) NOT NULL DEFAULT 'pending',
  `author_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `type` tinyint(4) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_document_effects`
--

CREATE TABLE `lmczp_pacad_document_effects` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `document_id` bigint(20) UNSIGNED NOT NULL,
  `effect_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_document_tags`
--

CREATE TABLE `lmczp_pacad_document_tags` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `document_id` bigint(20) UNSIGNED NOT NULL,
  `tag_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_document_zones`
--

CREATE TABLE `lmczp_pacad_document_zones` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `document_id` bigint(20) UNSIGNED NOT NULL,
  `region_id` bigint(20) UNSIGNED NOT NULL,
  `department_id` bigint(20) UNSIGNED DEFAULT NULL,
  `commune_id` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_donation_extensions`
--

CREATE TABLE `lmczp_pacad_donation_extensions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `donation_id` bigint(20) UNSIGNED NOT NULL,
  `extension_date` date NOT NULL,
  `revision_id` int(11) NOT NULL DEFAULT 0,
  `revision_author_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_pcc_indicators_data`
--

CREATE TABLE `lmczp_pacad_pcc_indicators_data` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `year` int(11) NOT NULL,
  `pcc_indicator_id` bigint(20) UNSIGNED NOT NULL,
  `interval_1` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `interval_2` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_projects`
--

CREATE TABLE `lmczp_pacad_projects` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `post_id` bigint(20) UNSIGNED NOT NULL,
  `revision_id` int(11) NOT NULL DEFAULT 0,
  `status` varchar(191) DEFAULT 'pending',
  `title` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `target_population` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`target_population`)),
  `overall_budget` double(12,2) NOT NULL,
  `info_hash` varchar(255) DEFAULT NULL,
  `info_revision` int(11) DEFAULT NULL,
  `extensions_hash` varchar(255) DEFAULT NULL,
  `extensions_revision` int(11) DEFAULT NULL,
  `effects_hash` varchar(255) DEFAULT NULL,
  `effects_revision` int(11) DEFAULT NULL,
  `agencies_hash` varchar(255) DEFAULT NULL,
  `agencies_revision` int(11) DEFAULT NULL,
  `zones_hash` varchar(255) DEFAULT NULL,
  `zones_revision` int(11) DEFAULT NULL,
  `donations_hash` varchar(255) DEFAULT NULL,
  `donations_revision` int(11) DEFAULT NULL,
  `fundings_hash` varchar(255) DEFAULT NULL,
  `fundings_revision` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_project_agencies`
--

CREATE TABLE `lmczp_pacad_project_agencies` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `agency_id` bigint(20) UNSIGNED NOT NULL,
  `focal_point_id` bigint(20) UNSIGNED DEFAULT NULL,
  `focal_type` varchar(255) NOT NULL,
  `revision_id` int(11) NOT NULL DEFAULT 0,
  `revision_author_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_project_donations`
--

CREATE TABLE `lmczp_pacad_project_donations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `donor_id` bigint(20) UNSIGNED NOT NULL,
  `amount` double(12,2) NOT NULL,
  `effect_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`effect_ids`)),
  `signature_date` date NOT NULL,
  `expiry_date` date NOT NULL,
  `extensions_hash` varchar(255) DEFAULT NULL,
  `extensions_revision` int(11) DEFAULT NULL,
  `budgets_hash` varchar(255) DEFAULT NULL,
  `budgets_revision` int(11) DEFAULT NULL,
  `revision_id` int(11) NOT NULL DEFAULT 0,
  `revision_author_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_project_donation_budgets`
--

CREATE TABLE `lmczp_pacad_project_donation_budgets` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `donation_id` bigint(20) UNSIGNED NOT NULL,
  `sdg_id` bigint(20) UNSIGNED NOT NULL,
  `amount_disbursed` double(12,2) NOT NULL DEFAULT 0.00,
  `amount_mobilized` double(12,2) NOT NULL DEFAULT 0.00,
  `percentage_disbursed` double(8,2) NOT NULL DEFAULT 0.00,
  `percentage_mobilized` double(8,2) NOT NULL DEFAULT 0.00,
  `available_budget` double(12,2) NOT NULL DEFAULT 0.00,
  `diff_amount_disbursed` double(12,2) DEFAULT NULL,
  `diff_amount_mobilized` double(12,2) DEFAULT NULL,
  `diff_percentage_disbursed` double(8,2) DEFAULT NULL,
  `diff_percentage_mobilized` double(8,2) DEFAULT NULL,
  `diff_available_budget` double(12,2) DEFAULT NULL,
  `revision_id` int(11) NOT NULL DEFAULT 0,
  `revision_author_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_project_effects`
--

CREATE TABLE `lmczp_pacad_project_effects` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `effect_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`data`)),
  `sdg_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`sdg_ids`)),
  `pdes_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`pdes_ids`)),
  `uniss_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`uniss_ids`)),
  `revision_id` int(11) NOT NULL DEFAULT 0,
  `revision_author_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_project_extensions`
--

CREATE TABLE `lmczp_pacad_project_extensions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `extension_date` date NOT NULL,
  `revision_id` int(11) NOT NULL DEFAULT 0,
  `revision_author_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_project_fundings`
--

CREATE TABLE `lmczp_pacad_project_fundings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `agency_id` bigint(20) UNSIGNED NOT NULL,
  `received` double(12,2) NOT NULL,
  `disbursed` double(12,2) NOT NULL,
  `diff_received` double(12,2) DEFAULT NULL,
  `diff_disbursed` double(12,2) DEFAULT NULL,
  `revision_id` int(11) NOT NULL DEFAULT 0,
  `revision_author_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_project_info`
--

CREATE TABLE `lmczp_pacad_project_info` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `status` varchar(255) NOT NULL,
  `progress` varchar(255) NOT NULL,
  `sector_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`sector_ids`)),
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `revision_id` int(11) NOT NULL DEFAULT 0,
  `revision_author_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_project_zones`
--

CREATE TABLE `lmczp_pacad_project_zones` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `region_id` bigint(20) UNSIGNED NOT NULL,
  `department_id` bigint(20) UNSIGNED DEFAULT NULL,
  `commune_id` bigint(20) UNSIGNED DEFAULT NULL,
  `revision_id` int(11) NOT NULL DEFAULT 0,
  `revision_author_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_sms_alerts_reports`
--

CREATE TABLE `lmczp_pacad_sms_alerts_reports` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `sent_by` bigint(20) UNSIGNED NOT NULL,
  `external_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `message` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `sent_to` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`sent_to`)),
  `status` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'pending',
  `request_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`request_data`)),
  `response_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`response_data`)),
  `response_code` int(11) DEFAULT NULL,
  `callback_index` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_pacad_sms_alerts_report_data`
--

CREATE TABLE `lmczp_pacad_sms_alerts_report_data` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `sms_alerts_report_id` bigint(20) UNSIGNED NOT NULL,
  `status` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'pending',
  `phone_number` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `external_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `callback_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`callback_data`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_postmeta`
--

CREATE TABLE `lmczp_postmeta` (
  `meta_id` bigint(20) UNSIGNED NOT NULL,
  `post_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_rank_math_internal_links`
--

CREATE TABLE `lmczp_rank_math_internal_links` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `post_id` bigint(20) UNSIGNED NOT NULL,
  `target_post_id` bigint(20) UNSIGNED NOT NULL,
  `type` varchar(8) COLLATE utf8mb4_unicode_520_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_rank_math_internal_meta`
--

CREATE TABLE `lmczp_rank_math_internal_meta` (
  `object_id` bigint(20) UNSIGNED NOT NULL,
  `internal_link_count` int(10) UNSIGNED DEFAULT 0,
  `external_link_count` int(10) UNSIGNED DEFAULT 0,
  `incoming_link_count` int(10) UNSIGNED DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_snippets`
--

CREATE TABLE `lmczp_snippets` (
  `id` bigint(20) NOT NULL,
  `name` tinytext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `code` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `tags` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `scope` varchar(15) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'global',
  `priority` smallint(6) NOT NULL DEFAULT 10,
  `active` tinyint(1) NOT NULL DEFAULT 0,
  `modified` datetime NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_tec_events`
--

CREATE TABLE `lmczp_tec_events` (
  `event_id` bigint(20) UNSIGNED NOT NULL,
  `post_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime DEFAULT NULL,
  `timezone` varchar(30) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'UTC',
  `start_date_utc` datetime NOT NULL,
  `end_date_utc` datetime DEFAULT NULL,
  `duration` mediumint(9) DEFAULT 7200,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `hash` varchar(40) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `rset` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_tec_occurrences`
--

CREATE TABLE `lmczp_tec_occurrences` (
  `occurrence_id` bigint(20) UNSIGNED NOT NULL,
  `event_id` bigint(20) UNSIGNED NOT NULL,
  `post_id` bigint(20) UNSIGNED NOT NULL,
  `start_date` datetime NOT NULL,
  `start_date_utc` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `end_date_utc` datetime NOT NULL,
  `duration` mediumint(9) DEFAULT 7200,
  `hash` varchar(40) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `has_recurrence` tinyint(1) DEFAULT 0,
  `sequence` bigint(20) UNSIGNED DEFAULT 0,
  `is_rdate` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_tec_series_relationships`
--

CREATE TABLE `lmczp_tec_series_relationships` (
  `relationship_id` bigint(20) UNSIGNED NOT NULL,
  `series_post_id` bigint(20) UNSIGNED NOT NULL,
  `event_id` bigint(20) UNSIGNED NOT NULL,
  `event_post_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_termmeta`
--

CREATE TABLE `lmczp_termmeta` (
  `meta_id` bigint(20) UNSIGNED NOT NULL,
  `term_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_terms`
--

CREATE TABLE `lmczp_terms` (
  `term_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(200) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `slug` varchar(200) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `term_group` bigint(10) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_term_relationships`
--

CREATE TABLE `lmczp_term_relationships` (
  `object_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `term_taxonomy_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `term_order` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_term_taxonomy`
--

CREATE TABLE `lmczp_term_taxonomy` (
  `term_taxonomy_id` bigint(20) UNSIGNED NOT NULL,
  `term_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `taxonomy` varchar(32) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `parent` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `count` bigint(20) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_um_metadata`
--

CREATE TABLE `lmczp_um_metadata` (
  `umeta_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `um_key` varchar(255) DEFAULT NULL,
  `um_value` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_usermeta`
--

CREATE TABLE `lmczp_usermeta` (
  `umeta_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_users`
--

CREATE TABLE `lmczp_users` (
  `ID` bigint(20) UNSIGNED NOT NULL,
  `user_login` varchar(60) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_pass` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_nicename` varchar(50) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_email` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_url` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_registered` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `user_activation_key` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `user_status` int(11) NOT NULL DEFAULT 0,
  `display_name` varchar(250) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpda_org_chart_popup_theme`
--

CREATE TABLE `lmczp_wpda_org_chart_popup_theme` (
  `id` int(10) NOT NULL,
  `name` varchar(512) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `option_value` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `default` tinyint(4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpda_org_chart_theme`
--

CREATE TABLE `lmczp_wpda_org_chart_theme` (
  `id` int(10) NOT NULL,
  `name` varchar(512) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `option_value` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `default` tinyint(4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpda_org_chart_tree`
--

CREATE TABLE `lmczp_wpda_org_chart_tree` (
  `id` int(10) NOT NULL,
  `name` varchar(512) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `tree_nodes` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpforms_entries`
--

CREATE TABLE `lmczp_wpforms_entries` (
  `entry_id` bigint(20) NOT NULL,
  `form_id` bigint(20) NOT NULL,
  `post_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `status` varchar(30) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `type` varchar(30) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `viewed` tinyint(1) DEFAULT 0,
  `starred` tinyint(1) DEFAULT 0,
  `fields` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `meta` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `date` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  `ip_address` varchar(128) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `user_agent` varchar(256) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `user_uuid` varchar(36) COLLATE utf8mb4_unicode_520_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpforms_entry_fields`
--

CREATE TABLE `lmczp_wpforms_entry_fields` (
  `id` bigint(20) NOT NULL,
  `entry_id` bigint(20) NOT NULL,
  `form_id` bigint(20) NOT NULL,
  `field_id` int(11) NOT NULL,
  `value` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpforms_entry_meta`
--

CREATE TABLE `lmczp_wpforms_entry_meta` (
  `id` bigint(20) NOT NULL,
  `entry_id` bigint(20) NOT NULL,
  `form_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `status` varchar(30) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `data` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpforms_tasks_meta`
--

CREATE TABLE `lmczp_wpforms_tasks_meta` (
  `id` bigint(20) NOT NULL,
  `action` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `data` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpie_template`
--

CREATE TABLE `lmczp_wpie_template` (
  `id` int(11) NOT NULL,
  `status` varchar(25) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `opration` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `username` varchar(60) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `unique_id` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `opration_type` varchar(100) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `options` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `process_log` varchar(255) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `process_lock` int(11) DEFAULT NULL,
  `create_date` datetime NOT NULL,
  `last_update_date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpinventory_category`
--

CREATE TABLE `lmczp_wpinventory_category` (
  `category_id` int(11) NOT NULL,
  `category_name` varchar(255) NOT NULL,
  `category_description` varchar(255) NOT NULL,
  `category_slug` varchar(255) DEFAULT NULL,
  `category_sort_order` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpinventory_image`
--

CREATE TABLE `lmczp_wpinventory_image` (
  `image_id` int(11) NOT NULL,
  `inventory_id` int(11) NOT NULL,
  `post_id` varchar(255) NOT NULL,
  `image` text NOT NULL,
  `thumbnail` text NOT NULL,
  `medium` text NOT NULL,
  `large` text NOT NULL,
  `image_sort_order` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpinventory_item`
--

CREATE TABLE `lmczp_wpinventory_item` (
  `inventory_id` int(11) NOT NULL,
  `inventory_number` text NOT NULL,
  `inventory_name` text DEFAULT NULL,
  `inventory_description` text DEFAULT NULL,
  `inventory_size` text DEFAULT NULL,
  `inventory_manufacturer` text DEFAULT NULL,
  `inventory_make` text DEFAULT NULL,
  `inventory_model` text DEFAULT NULL,
  `inventory_year` text DEFAULT NULL,
  `inventory_serial` text DEFAULT NULL,
  `inventory_fob` text DEFAULT NULL,
  `inventory_quantity` int(11) NOT NULL DEFAULT 0,
  `inventory_quantity_reserved` int(11) NOT NULL DEFAULT 0,
  `inventory_price` float DEFAULT NULL,
  `inventory_slug` varchar(255) DEFAULT NULL,
  `inventory_sort_order` int(11) NOT NULL DEFAULT 0,
  `category_id` int(11) NOT NULL DEFAULT 1,
  `user_id` int(11) NOT NULL,
  `inventory_date_added` datetime DEFAULT NULL,
  `inventory_date_updated` datetime DEFAULT NULL,
  `inventory_updated_by` int(11) DEFAULT NULL,
  `inventory_status` int(11) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpinventory_label`
--

CREATE TABLE `lmczp_wpinventory_label` (
  `label_id` int(11) NOT NULL,
  `label_field` varchar(255) NOT NULL,
  `label_label` varchar(255) NOT NULL,
  `is_used` tinyint(4) NOT NULL DEFAULT 1,
  `is_numeric` tinyint(4) NOT NULL DEFAULT 0,
  `include_in_sort` tinyint(4) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpinventory_media`
--

CREATE TABLE `lmczp_wpinventory_media` (
  `media_id` int(11) NOT NULL,
  `inventory_id` int(11) NOT NULL,
  `media_title` varchar(255) NOT NULL,
  `media` text NOT NULL,
  `media_sort_order` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpinventory_reservation`
--

CREATE TABLE `lmczp_wpinventory_reservation` (
  `reservation_id` int(11) NOT NULL,
  `reservation_date` datetime NOT NULL DEFAULT current_timestamp(),
  `reservation_ip_address` varchar(50) NOT NULL DEFAULT '',
  `reservation_email_address` varchar(100) NOT NULL DEFAULT '',
  `reservation_form_data` text DEFAULT '',
  `reservation_total` float NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpinventory_reservation_item`
--

CREATE TABLE `lmczp_wpinventory_reservation_item` (
  `reservation_item_id` int(11) NOT NULL,
  `reservation_id` int(11) NOT NULL,
  `inventory_id` int(11) NOT NULL,
  `reservation_quantity` float NOT NULL,
  `reservation_item_cost` float NOT NULL DEFAULT 0,
  `reservation_item_price` float NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpinventory_status`
--

CREATE TABLE `lmczp_wpinventory_status` (
  `status_id` int(11) NOT NULL,
  `status_name` varchar(255) NOT NULL,
  `status_description` varchar(255) NOT NULL,
  `status_sort_order` int(11) NOT NULL DEFAULT 0,
  `is_active` tinyint(4) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpmailsmtp_debug_events`
--

CREATE TABLE `lmczp_wpmailsmtp_debug_events` (
  `id` int(10) UNSIGNED NOT NULL,
  `content` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `initiator` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `event_type` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `lmczp_wpmailsmtp_tasks_meta`
--

CREATE TABLE `lmczp_wpmailsmtp_tasks_meta` (
  `id` bigint(20) NOT NULL,
  `action` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `data` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_actionscheduler_actions`
--

CREATE TABLE `wp_784555_actionscheduler_actions` (
  `action_id` bigint(20) UNSIGNED NOT NULL,
  `hook` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `scheduled_date_gmt` datetime DEFAULT '0000-00-00 00:00:00',
  `scheduled_date_local` datetime DEFAULT '0000-00-00 00:00:00',
  `args` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `schedule` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `group_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `attempts` int(11) NOT NULL DEFAULT 0,
  `last_attempt_gmt` datetime DEFAULT '0000-00-00 00:00:00',
  `last_attempt_local` datetime DEFAULT '0000-00-00 00:00:00',
  `claim_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `extended_args` varchar(8000) COLLATE utf8mb4_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_actionscheduler_claims`
--

CREATE TABLE `wp_784555_actionscheduler_claims` (
  `claim_id` bigint(20) UNSIGNED NOT NULL,
  `date_created_gmt` datetime NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_actionscheduler_groups`
--

CREATE TABLE `wp_784555_actionscheduler_groups` (
  `group_id` bigint(20) UNSIGNED NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `wp_784555_actionscheduler_groups`
--

INSERT INTO `wp_784555_actionscheduler_groups` (`group_id`, `slug`) VALUES
(1, 'action-scheduler-migration'),
(2, 'wpforms'),
(3, 'wp_mail_smtp');

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_actionscheduler_logs`
--

CREATE TABLE `wp_784555_actionscheduler_logs` (
  `log_id` bigint(20) UNSIGNED NOT NULL,
  `action_id` bigint(20) UNSIGNED NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `log_date_gmt` datetime DEFAULT '0000-00-00 00:00:00',
  `log_date_local` datetime DEFAULT '0000-00-00 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_action`
--

CREATE TABLE `wp_784555_acym_action` (
  `id` int(11) NOT NULL,
  `condition_id` int(11) NOT NULL,
  `actions` longtext DEFAULT NULL,
  `filters` longtext DEFAULT NULL,
  `order` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_automation`
--

CREATE TABLE `wp_784555_acym_automation` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` longtext DEFAULT NULL,
  `active` tinyint(3) NOT NULL,
  `report` text DEFAULT NULL,
  `tree` longtext DEFAULT NULL,
  `admin` tinyint(3) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_campaign`
--

CREATE TABLE `wp_784555_acym_campaign` (
  `id` int(11) NOT NULL,
  `sending_date` datetime DEFAULT NULL,
  `draft` tinyint(1) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `mail_id` int(11) DEFAULT NULL,
  `sent` tinyint(1) NOT NULL DEFAULT 0,
  `sending_type` varchar(16) DEFAULT NULL,
  `sending_params` text DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `last_generated` int(11) DEFAULT NULL,
  `next_trigger` int(11) DEFAULT NULL,
  `visible` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_condition`
--

CREATE TABLE `wp_784555_acym_condition` (
  `id` int(11) NOT NULL,
  `step_id` int(11) NOT NULL,
  `conditions` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_configuration`
--

CREATE TABLE `wp_784555_acym_configuration` (
  `name` varchar(255) NOT NULL,
  `value` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_custom_zone`
--

CREATE TABLE `wp_784555_acym_custom_zone` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `content` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_field`
--

CREATE TABLE `wp_784555_acym_field` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `value` longtext DEFAULT NULL,
  `active` tinyint(3) NOT NULL,
  `default_value` longtext DEFAULT NULL,
  `required` tinyint(3) DEFAULT NULL,
  `ordering` int(11) NOT NULL,
  `option` longtext DEFAULT NULL,
  `core` tinyint(3) DEFAULT NULL,
  `backend_edition` tinyint(3) DEFAULT NULL,
  `backend_listing` tinyint(3) DEFAULT NULL,
  `frontend_edition` tinyint(3) DEFAULT NULL,
  `frontend_listing` tinyint(3) DEFAULT NULL,
  `access` varchar(255) DEFAULT NULL,
  `namekey` varchar(255) NOT NULL,
  `translation` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_followup`
--

CREATE TABLE `wp_784555_acym_followup` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `display_name` varchar(255) NOT NULL,
  `creation_date` datetime NOT NULL,
  `trigger` varchar(50) DEFAULT NULL,
  `condition` longtext DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `send_once` tinyint(1) NOT NULL DEFAULT 1,
  `list_id` int(11) DEFAULT NULL,
  `last_trigger` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_followup_has_mail`
--

CREATE TABLE `wp_784555_acym_followup_has_mail` (
  `mail_id` int(11) NOT NULL,
  `followup_id` int(11) NOT NULL,
  `delay` int(11) NOT NULL,
  `delay_unit` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_form`
--

CREATE TABLE `wp_784555_acym_form` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `creation_date` datetime NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `type` varchar(20) NOT NULL,
  `lists_options` longtext DEFAULT NULL,
  `fields_options` longtext DEFAULT NULL,
  `style_options` longtext DEFAULT NULL,
  `button_options` longtext DEFAULT NULL,
  `image_options` longtext DEFAULT NULL,
  `termspolicy_options` longtext DEFAULT NULL,
  `cookie` varchar(30) DEFAULT NULL,
  `pages` text DEFAULT NULL,
  `redirection_options` text DEFAULT NULL,
  `message_options` text DEFAULT NULL,
  `display_options` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_history`
--

CREATE TABLE `wp_784555_acym_history` (
  `user_id` int(11) NOT NULL,
  `date` int(11) NOT NULL,
  `ip` varchar(50) DEFAULT NULL,
  `action` varchar(50) NOT NULL,
  `data` text DEFAULT NULL,
  `source` text DEFAULT NULL,
  `mail_id` mediumint(9) DEFAULT NULL,
  `unsubscribe_reason` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_list`
--

CREATE TABLE `wp_784555_acym_list` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `display_name` varchar(255) DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `visible` tinyint(1) NOT NULL DEFAULT 1,
  `clean` tinyint(1) DEFAULT NULL,
  `color` varchar(30) DEFAULT NULL,
  `creation_date` datetime DEFAULT NULL,
  `welcome_id` int(11) DEFAULT NULL,
  `unsubscribe_id` int(11) DEFAULT NULL,
  `cms_user_id` int(11) NOT NULL,
  `access` varchar(50) NOT NULL DEFAULT '',
  `description` text NOT NULL,
  `tracking` tinyint(1) NOT NULL DEFAULT 1,
  `type` varchar(20) NOT NULL DEFAULT 'standard',
  `translation` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_mail`
--

CREATE TABLE `wp_784555_acym_mail` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `creation_date` datetime NOT NULL,
  `thumbnail` longtext DEFAULT NULL,
  `drag_editor` tinyint(1) DEFAULT NULL,
  `type` varchar(30) NOT NULL,
  `body` longtext NOT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `from_name` varchar(100) DEFAULT NULL,
  `from_email` varchar(100) DEFAULT NULL,
  `reply_to_name` varchar(100) DEFAULT NULL,
  `reply_to_email` varchar(100) DEFAULT NULL,
  `bcc` varchar(255) DEFAULT NULL,
  `settings` text DEFAULT NULL,
  `stylesheet` text DEFAULT NULL,
  `attachments` text DEFAULT NULL,
  `creator_id` int(11) NOT NULL,
  `mail_settings` text DEFAULT NULL,
  `headers` text DEFAULT NULL,
  `autosave` longtext DEFAULT NULL,
  `preheader` text DEFAULT NULL,
  `links_language` varchar(20) NOT NULL DEFAULT '',
  `access` varchar(50) NOT NULL DEFAULT '',
  `tracking` tinyint(1) NOT NULL DEFAULT 1,
  `language` varchar(20) NOT NULL DEFAULT '',
  `parent_id` int(11) DEFAULT NULL,
  `translation` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_mail_has_list`
--

CREATE TABLE `wp_784555_acym_mail_has_list` (
  `mail_id` int(11) NOT NULL,
  `list_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_mail_override`
--

CREATE TABLE `wp_784555_acym_mail_override` (
  `id` int(11) NOT NULL,
  `mail_id` int(11) NOT NULL,
  `description` varchar(255) NOT NULL,
  `source` varchar(20) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `base_subject` text NOT NULL,
  `base_body` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_mail_stat`
--

CREATE TABLE `wp_784555_acym_mail_stat` (
  `mail_id` int(11) NOT NULL,
  `total_subscribers` int(11) NOT NULL DEFAULT 0,
  `sent` int(11) DEFAULT 0,
  `send_date` datetime DEFAULT NULL,
  `fail` int(11) DEFAULT 0,
  `open_unique` int(11) NOT NULL DEFAULT 0,
  `open_total` int(11) NOT NULL DEFAULT 0,
  `bounce_unique` mediumint(8) NOT NULL DEFAULT 0,
  `bounce_details` longtext DEFAULT NULL,
  `unsubscribe_total` int(11) NOT NULL DEFAULT 0,
  `tracking_sale` float DEFAULT NULL,
  `currency` varchar(5) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_plugin`
--

CREATE TABLE `wp_784555_acym_plugin` (
  `id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `folder_name` varchar(100) NOT NULL,
  `version` varchar(10) DEFAULT NULL,
  `active` int(11) NOT NULL,
  `category` varchar(100) NOT NULL,
  `level` varchar(50) NOT NULL,
  `uptodate` int(11) NOT NULL,
  `features` varchar(255) NOT NULL,
  `description` longtext NOT NULL,
  `latest_version` varchar(10) NOT NULL,
  `settings` longtext DEFAULT NULL,
  `type` varchar(20) NOT NULL DEFAULT 'ADDON'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_queue`
--

CREATE TABLE `wp_784555_acym_queue` (
  `mail_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `sending_date` datetime NOT NULL,
  `priority` int(11) NOT NULL DEFAULT 2,
  `try` tinyint(4) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_rule`
--

CREATE TABLE `wp_784555_acym_rule` (
  `id` int(11) NOT NULL,
  `name` varchar(250) NOT NULL,
  `ordering` smallint(6) DEFAULT NULL,
  `regex` text NOT NULL,
  `executed_on` text NOT NULL,
  `action_message` text NOT NULL,
  `action_user` text NOT NULL,
  `active` tinyint(3) NOT NULL,
  `increment_stats` tinyint(3) NOT NULL,
  `execute_action_after` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_segment`
--

CREATE TABLE `wp_784555_acym_segment` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `creation_date` datetime NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `filters` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_step`
--

CREATE TABLE `wp_784555_acym_step` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `triggers` longtext DEFAULT NULL,
  `automation_id` int(11) NOT NULL,
  `last_execution` int(11) DEFAULT NULL,
  `next_execution` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_tag`
--

CREATE TABLE `wp_784555_acym_tag` (
  `name` varchar(50) NOT NULL,
  `type` varchar(20) NOT NULL,
  `id_element` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_url`
--

CREATE TABLE `wp_784555_acym_url` (
  `id` int(11) NOT NULL,
  `name` longtext DEFAULT NULL,
  `url` longtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_url_click`
--

CREATE TABLE `wp_784555_acym_url_click` (
  `mail_id` int(11) NOT NULL,
  `url_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `click` int(11) NOT NULL DEFAULT 0,
  `date_click` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_user`
--

CREATE TABLE `wp_784555_acym_user` (
  `id` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `creation_date` datetime NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT 1,
  `cms_id` int(11) NOT NULL DEFAULT 0,
  `source` varchar(255) DEFAULT NULL,
  `confirmed` tinyint(1) NOT NULL DEFAULT 0,
  `key` varchar(30) DEFAULT NULL,
  `automation` varchar(50) NOT NULL DEFAULT '',
  `confirmation_date` datetime DEFAULT NULL,
  `confirmation_ip` varchar(50) DEFAULT NULL,
  `tracking` tinyint(1) NOT NULL DEFAULT 1,
  `language` varchar(20) NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_user_has_field`
--

CREATE TABLE `wp_784555_acym_user_has_field` (
  `user_id` int(11) NOT NULL,
  `field_id` int(11) NOT NULL,
  `value` longtext DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_user_has_list`
--

CREATE TABLE `wp_784555_acym_user_has_list` (
  `user_id` int(11) NOT NULL,
  `list_id` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `subscription_date` datetime NOT NULL,
  `unsubscribe_date` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_acym_user_stat`
--

CREATE TABLE `wp_784555_acym_user_stat` (
  `user_id` int(11) NOT NULL,
  `mail_id` int(11) NOT NULL,
  `send_date` datetime DEFAULT NULL,
  `fail` int(11) NOT NULL DEFAULT 0,
  `sent` int(11) NOT NULL DEFAULT 0,
  `open` int(11) NOT NULL DEFAULT 0,
  `open_date` datetime DEFAULT NULL,
  `bounce` tinyint(4) NOT NULL DEFAULT 0,
  `bounce_rule` varchar(255) DEFAULT NULL,
  `tracking_sale` float DEFAULT NULL,
  `currency` varchar(10) DEFAULT NULL,
  `unsubscribe` int(11) NOT NULL DEFAULT 0,
  `device` varchar(50) DEFAULT NULL,
  `opened_with` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_aryo_activity_log`
--

CREATE TABLE `wp_784555_aryo_activity_log` (
  `histid` int(11) NOT NULL,
  `user_caps` varchar(70) NOT NULL DEFAULT 'guest',
  `action` varchar(255) NOT NULL,
  `object_type` varchar(255) NOT NULL,
  `object_subtype` varchar(255) NOT NULL DEFAULT '',
  `object_name` varchar(255) NOT NULL,
  `object_id` int(11) NOT NULL DEFAULT 0,
  `user_id` int(11) NOT NULL DEFAULT 0,
  `hist_ip` varchar(55) NOT NULL DEFAULT '127.0.0.1',
  `hist_time` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_commentmeta`
--

CREATE TABLE `wp_784555_commentmeta` (
  `meta_id` bigint(20) UNSIGNED NOT NULL,
  `comment_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_comments`
--

CREATE TABLE `wp_784555_comments` (
  `comment_ID` bigint(20) UNSIGNED NOT NULL,
  `comment_post_ID` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `comment_author` tinytext COLLATE utf8mb4_unicode_ci NOT NULL,
  `comment_author_email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `comment_author_url` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `comment_author_IP` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `comment_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `comment_date_gmt` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `comment_content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `comment_karma` int(11) NOT NULL DEFAULT 0,
  `comment_approved` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1',
  `comment_agent` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `comment_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'comment',
  `comment_parent` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_e_events`
--

CREATE TABLE `wp_784555_e_events` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `event_data` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_fbv`
--

CREATE TABLE `wp_784555_fbv` (
  `id` int(11) UNSIGNED NOT NULL,
  `name` varchar(250) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `parent` int(11) NOT NULL DEFAULT 0,
  `type` int(2) NOT NULL DEFAULT 0,
  `ord` int(11) DEFAULT 0,
  `created_by` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_fbv_attachment_folder`
--

CREATE TABLE `wp_784555_fbv_attachment_folder` (
  `folder_id` int(11) UNSIGNED NOT NULL,
  `attachment_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_links`
--

CREATE TABLE `wp_784555_links` (
  `link_id` bigint(20) UNSIGNED NOT NULL,
  `link_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `link_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `link_image` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `link_target` varchar(25) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `link_description` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `link_visible` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Y',
  `link_owner` bigint(20) UNSIGNED NOT NULL DEFAULT 1,
  `link_rating` int(11) NOT NULL DEFAULT 0,
  `link_updated` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `link_rel` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `link_notes` mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `link_rss` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_maps`
--

CREATE TABLE `wp_784555_mapsvg6_maps` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT '',
  `options` longtext COLLATE utf8_unicode_ci DEFAULT '',
  `svgFilePath` varchar(500) COLLATE utf8_unicode_ci DEFAULT NULL,
  `svgFileLastChanged` int(11) UNSIGNED DEFAULT NULL,
  `version` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` tinyint(1) UNSIGNED DEFAULT 1,
  `statusChangedAt` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_objects_1`
--

CREATE TABLE `wp_784555_mapsvg6_objects_1` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_img` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regions` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `category_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `images` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_objects_2`
--

CREATE TABLE `wp_784555_mapsvg6_objects_2` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_img` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regions` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `images` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_objects_3`
--

CREATE TABLE `wp_784555_mapsvg6_objects_3` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_img` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regions` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `images` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_objects_4`
--

CREATE TABLE `wp_784555_mapsvg6_objects_4` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_img` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regions` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `images` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_objects_5`
--

CREATE TABLE `wp_784555_mapsvg6_objects_5` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_img` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regions` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `images` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_objects_6`
--

CREATE TABLE `wp_784555_mapsvg6_objects_6` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_img` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regions` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `images` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_objects_7`
--

CREATE TABLE `wp_784555_mapsvg6_objects_7` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_img` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regions` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `images` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_objects_8`
--

CREATE TABLE `wp_784555_mapsvg6_objects_8` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_img` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regions` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `images` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_objects_9`
--

CREATE TABLE `wp_784555_mapsvg6_objects_9` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_img` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regions` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `images` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_objects_10`
--

CREATE TABLE `wp_784555_mapsvg6_objects_10` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_img` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regions` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `images` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_objects_11`
--

CREATE TABLE `wp_784555_mapsvg6_objects_11` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_img` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regions` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `category_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `images` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_posts_page`
--

CREATE TABLE `wp_784555_mapsvg6_posts_page` (
  `id` int(11) NOT NULL,
  `location` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_lat` float(10,7) DEFAULT NULL,
  `location_lng` float(10,7) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `location_address` text COLLATE utf8_unicode_ci DEFAULT NULL,
  `location_img` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `post` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_r2o`
--

CREATE TABLE `wp_784555_mapsvg6_r2o` (
  `objects_table` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `regions_table` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `region_id` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `object_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_regions_1`
--

CREATE TABLE `wp_784555_mapsvg6_regions_1` (
  `id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'Enabled',
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `population` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `densite` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_regions_2`
--

CREATE TABLE `wp_784555_mapsvg6_regions_2` (
  `id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'Enabled',
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_regions_3`
--

CREATE TABLE `wp_784555_mapsvg6_regions_3` (
  `id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'Enabled',
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_regions_4`
--

CREATE TABLE `wp_784555_mapsvg6_regions_4` (
  `id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'Enabled',
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_regions_5`
--

CREATE TABLE `wp_784555_mapsvg6_regions_5` (
  `id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'Enabled',
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_regions_6`
--

CREATE TABLE `wp_784555_mapsvg6_regions_6` (
  `id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'Enabled',
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_regions_7`
--

CREATE TABLE `wp_784555_mapsvg6_regions_7` (
  `id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'Enabled',
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_regions_8`
--

CREATE TABLE `wp_784555_mapsvg6_regions_8` (
  `id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'Enabled',
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_regions_9`
--

CREATE TABLE `wp_784555_mapsvg6_regions_9` (
  `id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'Enabled',
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_regions_10`
--

CREATE TABLE `wp_784555_mapsvg6_regions_10` (
  `id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'Enabled',
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_regions_11`
--

CREATE TABLE `wp_784555_mapsvg6_regions_11` (
  `id` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` int(11) DEFAULT 1,
  `status_text` varchar(255) COLLATE utf8_unicode_ci DEFAULT 'Enabled',
  `link` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;



-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_schema`
--

CREATE TABLE `wp_784555_mapsvg6_schema` (
  `id` int(11) NOT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci DEFAULT '',
  `name` varchar(255) COLLATE utf8_unicode_ci DEFAULT '',
  `fields` longtext COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mapsvg6_settings`
--

CREATE TABLE `wp_784555_mapsvg6_settings` (
  `key` varchar(100) COLLATE utf8_unicode_ci NOT NULL,
  `value` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mec_attendees`
--

CREATE TABLE `wp_784555_mec_attendees` (
  `attendee_id` bigint(20) NOT NULL,
  `post_id` bigint(20) NOT NULL,
  `event_id` bigint(20) NOT NULL,
  `occurrence` int(11) NOT NULL,
  `email` varchar(50) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `first_name` varchar(50) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `last_name` varchar(50) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `data` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `count` int(11) DEFAULT 1,
  `verification` int(1) DEFAULT 0,
  `confirmation` int(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mec_bookings`
--

CREATE TABLE `wp_784555_mec_bookings` (
  `id` int(10) UNSIGNED NOT NULL,
  `booking_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED DEFAULT NULL,
  `transaction_id` varchar(20) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `event_id` int(10) UNSIGNED NOT NULL,
  `ticket_ids` varchar(655) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `seats` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `status` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'pending',
  `confirmed` tinyint(4) NOT NULL DEFAULT 0,
  `verified` tinyint(4) NOT NULL DEFAULT 0,
  `all_occurrences` tinyint(4) NOT NULL DEFAULT 0,
  `date` datetime NOT NULL,
  `timestamp` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mec_dates`
--

CREATE TABLE `wp_784555_mec_dates` (
  `id` int(10) UNSIGNED NOT NULL,
  `post_id` int(10) NOT NULL,
  `dstart` date NOT NULL,
  `dend` date NOT NULL,
  `tstart` int(11) UNSIGNED NOT NULL DEFAULT 0,
  `tend` int(11) UNSIGNED NOT NULL DEFAULT 0,
  `status` varchar(20) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT 'publish',
  `public` int(4) UNSIGNED NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mec_events`
--

CREATE TABLE `wp_784555_mec_events` (
  `id` int(10) NOT NULL,
  `post_id` int(10) NOT NULL,
  `start` date NOT NULL,
  `end` date NOT NULL,
  `repeat` tinyint(4) NOT NULL DEFAULT 0,
  `rinterval` varchar(10) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `year` varchar(80) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `month` varchar(80) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `day` varchar(80) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `week` varchar(80) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `weekday` varchar(80) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `weekdays` varchar(80) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `days` text COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `not_in_days` text COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `time_start` int(10) NOT NULL DEFAULT 0,
  `time_end` int(10) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mec_occurrences`
--

CREATE TABLE `wp_784555_mec_occurrences` (
  `id` int(10) UNSIGNED NOT NULL,
  `post_id` int(10) UNSIGNED NOT NULL,
  `occurrence` int(10) UNSIGNED NOT NULL,
  `params` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_mec_users`
--

CREATE TABLE `wp_784555_mec_users` (
  `id` int(11) NOT NULL,
  `first_name` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `last_name` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `email` varchar(127) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `reg` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_newsletter`
--

CREATE TABLE `wp_784555_newsletter` (
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `token` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `language` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `status` varchar(1) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'S',
  `id` int(11) NOT NULL,
  `profile` mediumtext COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated` int(11) NOT NULL DEFAULT 0,
  `last_activity` int(11) NOT NULL DEFAULT 0,
  `followup_step` tinyint(4) NOT NULL DEFAULT 0,
  `followup_time` bigint(20) NOT NULL DEFAULT 0,
  `followup` tinyint(4) NOT NULL DEFAULT 0,
  `surname` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `sex` char(1) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'n',
  `feed_time` bigint(20) NOT NULL DEFAULT 0,
  `feed` tinyint(4) NOT NULL DEFAULT 0,
  `referrer` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `ip` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `wp_user_id` int(11) NOT NULL DEFAULT 0,
  `http_referer` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `geo` tinyint(4) NOT NULL DEFAULT 0,
  `country` varchar(4) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `region` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `city` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `bounce_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `bounce_time` int(11) NOT NULL DEFAULT 0,
  `unsub_email_id` int(11) NOT NULL DEFAULT 0,
  `unsub_time` int(11) NOT NULL DEFAULT 0,
  `list_1` tinyint(4) NOT NULL DEFAULT 0,
  `list_2` tinyint(4) NOT NULL DEFAULT 0,
  `list_3` tinyint(4) NOT NULL DEFAULT 0,
  `list_4` tinyint(4) NOT NULL DEFAULT 0,
  `list_5` tinyint(4) NOT NULL DEFAULT 0,
  `list_6` tinyint(4) NOT NULL DEFAULT 0,
  `list_7` tinyint(4) NOT NULL DEFAULT 0,
  `list_8` tinyint(4) NOT NULL DEFAULT 0,
  `list_9` tinyint(4) NOT NULL DEFAULT 0,
  `list_10` tinyint(4) NOT NULL DEFAULT 0,
  `list_11` tinyint(4) NOT NULL DEFAULT 0,
  `list_12` tinyint(4) NOT NULL DEFAULT 0,
  `list_13` tinyint(4) NOT NULL DEFAULT 0,
  `list_14` tinyint(4) NOT NULL DEFAULT 0,
  `list_15` tinyint(4) NOT NULL DEFAULT 0,
  `list_16` tinyint(4) NOT NULL DEFAULT 0,
  `list_17` tinyint(4) NOT NULL DEFAULT 0,
  `list_18` tinyint(4) NOT NULL DEFAULT 0,
  `list_19` tinyint(4) NOT NULL DEFAULT 0,
  `list_20` tinyint(4) NOT NULL DEFAULT 0,
  `list_21` tinyint(4) NOT NULL DEFAULT 0,
  `list_22` tinyint(4) NOT NULL DEFAULT 0,
  `list_23` tinyint(4) NOT NULL DEFAULT 0,
  `list_24` tinyint(4) NOT NULL DEFAULT 0,
  `list_25` tinyint(4) NOT NULL DEFAULT 0,
  `list_26` tinyint(4) NOT NULL DEFAULT 0,
  `list_27` tinyint(4) NOT NULL DEFAULT 0,
  `list_28` tinyint(4) NOT NULL DEFAULT 0,
  `list_29` tinyint(4) NOT NULL DEFAULT 0,
  `list_30` tinyint(4) NOT NULL DEFAULT 0,
  `list_31` tinyint(4) NOT NULL DEFAULT 0,
  `list_32` tinyint(4) NOT NULL DEFAULT 0,
  `list_33` tinyint(4) NOT NULL DEFAULT 0,
  `list_34` tinyint(4) NOT NULL DEFAULT 0,
  `list_35` tinyint(4) NOT NULL DEFAULT 0,
  `list_36` tinyint(4) NOT NULL DEFAULT 0,
  `list_37` tinyint(4) NOT NULL DEFAULT 0,
  `list_38` tinyint(4) NOT NULL DEFAULT 0,
  `list_39` tinyint(4) NOT NULL DEFAULT 0,
  `list_40` tinyint(4) NOT NULL DEFAULT 0,
  `profile_1` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_2` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_3` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_4` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_5` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_6` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_7` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_8` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_9` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_10` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_11` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_12` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_13` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_14` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_15` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_16` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_17` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_18` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_19` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `profile_20` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `test` tinyint(4) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `wp_784555_newsletter`
--

INSERT INTO `wp_784555_newsletter` (`name`, `email`, `token`, `language`, `status`, `id`, `profile`, `created`, `updated`, `last_activity`, `followup_step`, `followup_time`, `followup`, `surname`, `sex`, `feed_time`, `feed`, `referrer`, `ip`, `wp_user_id`, `http_referer`, `geo`, `country`, `region`, `city`, `bounce_type`, `bounce_time`, `unsub_email_id`, `unsub_time`, `list_1`, `list_2`, `list_3`, `list_4`, `list_5`, `list_6`, `list_7`, `list_8`, `list_9`, `list_10`, `list_11`, `list_12`, `list_13`, `list_14`, `list_15`, `list_16`, `list_17`, `list_18`, `list_19`, `list_20`, `list_21`, `list_22`, `list_23`, `list_24`, `list_25`, `list_26`, `list_27`, `list_28`, `list_29`, `list_30`, `list_31`, `list_32`, `list_33`, `list_34`, `list_35`, `list_36`, `list_37`, `list_38`, `list_39`, `list_40`, `profile_1`, `profile_2`, `profile_3`, `profile_4`, `profile_5`, `profile_6`, `profile_7`, `profile_8`, `profile_9`, `profile_10`, `profile_11`, `profile_12`, `profile_13`, `profile_14`, `profile_15`, `profile_16`, `profile_17`, `profile_18`, `profile_19`, `profile_20`, `test`) VALUES
('', '<EMAIL>', 'c01264ce80', '', 'C', 1, NULL, '2022-03-22 10:04:00', 0, 0, 0, 0, 0, '', 'n', 0, 0, '', '', 0, '', 0, '', '', '', '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 1);

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_newsletter_emails`
--

CREATE TABLE `wp_784555_newsletter_emails` (
  `id` int(11) NOT NULL,
  `language` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `message` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created` timestamp NOT NULL DEFAULT current_timestamp(),
  `status` enum('new','sending','sent','paused','error') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'new',
  `total` int(11) NOT NULL DEFAULT 0,
  `last_id` int(11) NOT NULL DEFAULT 0,
  `sent` int(11) NOT NULL DEFAULT 0,
  `track` int(11) NOT NULL DEFAULT 1,
  `list` int(11) NOT NULL DEFAULT 0,
  `type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `query` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `editor` tinyint(4) NOT NULL DEFAULT 0,
  `sex` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `theme` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `message_text` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `preferences` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `send_on` int(11) NOT NULL DEFAULT 0,
  `token` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `options` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `private` tinyint(1) NOT NULL DEFAULT 0,
  `click_count` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `version` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `open_count` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `unsub_count` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `error_count` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `stats_time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `updated` int(10) UNSIGNED NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_newsletter_sent`
--

CREATE TABLE `wp_784555_newsletter_sent` (
  `email_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0,
  `open` tinyint(1) UNSIGNED NOT NULL DEFAULT 0,
  `time` int(10) UNSIGNED NOT NULL DEFAULT 0,
  `error` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `ip` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_newsletter_stats`
--

CREATE TABLE `wp_784555_newsletter_stats` (
  `id` int(11) NOT NULL,
  `created` timestamp NOT NULL DEFAULT current_timestamp(),
  `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `user_id` int(11) NOT NULL DEFAULT 0,
  `email_id` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
  `ip` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_newsletter_user_logs`
--

CREATE TABLE `wp_784555_newsletter_user_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL DEFAULT 0,
  `ip` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `source` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `data` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_ninja_table_items`
--

CREATE TABLE `wp_784555_ninja_table_items` (
  `id` int(11) NOT NULL,
  `position` int(11) DEFAULT NULL,
  `table_id` int(11) NOT NULL,
  `owner_id` int(11) DEFAULT NULL,
  `attribute` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `settings` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `value` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_options`
--

CREATE TABLE `wp_784555_options` (
  `option_id` bigint(20) UNSIGNED NOT NULL,
  `option_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `option_value` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `autoload` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'yes'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Déchargement des données de la table `wp_784555_options`
--


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_searchwp_index`
--

CREATE TABLE `wp_784555_searchwp_index` (
  `indexid` bigint(20) UNSIGNED NOT NULL,
  `token` bigint(20) UNSIGNED NOT NULL COMMENT 'Token ID',
  `occurrences` bigint(20) UNSIGNED NOT NULL COMMENT 'Number of token occurrences',
  `id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Source ID',
  `attribute` varchar(80) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Attribute name',
  `source` varchar(80) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Source name',
  `site` mediumint(9) UNSIGNED NOT NULL DEFAULT 1 COMMENT 'Site ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_searchwp_log`
--

CREATE TABLE `wp_784555_searchwp_log` (
  `logid` bigint(20) UNSIGNED NOT NULL,
  `query` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL COMMENT 'Search query for this search',
  `tstamp` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'Timestamp of this search',
  `hits` mediumint(9) UNSIGNED NOT NULL COMMENT 'Number of results for this search',
  `engine` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'default' COMMENT 'Engine used for this search',
  `site` mediumint(9) UNSIGNED NOT NULL DEFAULT 1 COMMENT 'Site where this search took place'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_searchwp_status`
--

CREATE TABLE `wp_784555_searchwp_status` (
  `statusid` bigint(20) UNSIGNED NOT NULL,
  `id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Source ID',
  `source` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `queued` timestamp NULL DEFAULT NULL COMMENT 'Whether this entry is queued for indexing',
  `indexed` timestamp NULL DEFAULT NULL COMMENT 'Whether this entry is indexed',
  `omitted` timestamp NULL DEFAULT NULL COMMENT 'Whether this entry is omitted',
  `site` bigint(20) UNSIGNED NOT NULL COMMENT 'Site ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_searchwp_tokens`
--

CREATE TABLE `wp_784555_searchwp_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT 'Canonical ID for this token',
  `token` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `stem` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_termmeta`
--

CREATE TABLE `wp_784555_termmeta` (
  `meta_id` bigint(20) UNSIGNED NOT NULL,
  `term_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_terms`
--

CREATE TABLE `wp_784555_terms` (
  `term_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `slug` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `term_group` bigint(10) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_term_relationships`
--

CREATE TABLE `wp_784555_term_relationships` (
  `object_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `term_taxonomy_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `term_order` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_term_taxonomy`
--

CREATE TABLE `wp_784555_term_taxonomy` (
  `term_taxonomy_id` bigint(20) UNSIGNED NOT NULL,
  `term_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `taxonomy` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `description` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `parent` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `count` bigint(20) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_um_metadata`
--

CREATE TABLE `wp_784555_um_metadata` (
  `umeta_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `um_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `um_value` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_um_notifications`
--

CREATE TABLE `wp_784555_um_notifications` (
  `id` int(11) UNSIGNED NOT NULL,
  `time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `user` tinytext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `status` tinytext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `photo` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `type` tinytext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `content` text COLLATE utf8mb4_unicode_520_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_usermeta`
--

CREATE TABLE `wp_784555_usermeta` (
  `umeta_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `meta_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `meta_value` longtext COLLATE utf8mb4_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_users`
--

CREATE TABLE `wp_784555_users` (
  `ID` bigint(20) UNSIGNED NOT NULL,
  `user_login` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `user_pass` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `user_nicename` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `user_email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `user_url` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `user_registered` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `user_activation_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `user_status` int(11) NOT NULL DEFAULT 0,
  `display_name` varchar(250) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wfu_dbxqueue`
--

CREATE TABLE `wp_784555_wfu_dbxqueue` (
  `iddbxqueue` mediumint(9) NOT NULL,
  `fileid` mediumint(9) NOT NULL,
  `priority` mediumint(9) NOT NULL,
  `status` mediumint(9) NOT NULL,
  `jobid` varchar(10) NOT NULL,
  `start_time` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wfu_log`
--

CREATE TABLE `wp_784555_wfu_log` (
  `idlog` mediumint(9) NOT NULL,
  `userid` int(11) NOT NULL,
  `uploaduserid` int(11) NOT NULL,
  `uploadtime` bigint(20) DEFAULT NULL,
  `sessionid` varchar(40) DEFAULT NULL,
  `filepath` text NOT NULL,
  `filehash` varchar(100) NOT NULL,
  `filesize` bigint(20) NOT NULL,
  `uploadid` varchar(20) NOT NULL,
  `pageid` mediumint(9) DEFAULT NULL,
  `blogid` mediumint(9) DEFAULT NULL,
  `sid` varchar(10) DEFAULT NULL,
  `date_from` datetime DEFAULT NULL,
  `date_to` datetime DEFAULT NULL,
  `action` varchar(20) NOT NULL,
  `linkedto` mediumint(9) DEFAULT NULL,
  `filedata` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wfu_userdata`
--

CREATE TABLE `wp_784555_wfu_userdata` (
  `iduserdata` mediumint(9) NOT NULL,
  `uploadid` varchar(20) NOT NULL,
  `property` varchar(100) NOT NULL,
  `propkey` mediumint(9) NOT NULL,
  `propvalue` text DEFAULT NULL,
  `date_from` datetime DEFAULT NULL,
  `date_to` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wpdatacharts`
--

CREATE TABLE `wp_784555_wpdatacharts` (
  `id` bigint(20) NOT NULL,
  `wpdatatable_id` bigint(20) NOT NULL,
  `title` varchar(255) NOT NULL,
  `engine` enum('google','highcharts','chartjs','apexcharts') NOT NULL,
  `type` varchar(255) NOT NULL,
  `json_render_data` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wpdatatables`
--

CREATE TABLE `wp_784555_wpdatatables` (
  `id` bigint(20) NOT NULL,
  `title` varchar(255) NOT NULL,
  `show_title` tinyint(1) NOT NULL DEFAULT 1,
  `table_type` varchar(55) NOT NULL,
  `connection` varchar(55) NOT NULL DEFAULT '',
  `content` text NOT NULL,
  `filtering` tinyint(1) NOT NULL DEFAULT 1,
  `filtering_form` tinyint(1) NOT NULL DEFAULT 0,
  `sorting` tinyint(1) NOT NULL DEFAULT 1,
  `tools` tinyint(1) NOT NULL DEFAULT 1,
  `server_side` tinyint(1) NOT NULL DEFAULT 0,
  `editable` tinyint(1) NOT NULL DEFAULT 0,
  `inline_editing` tinyint(1) NOT NULL DEFAULT 0,
  `popover_tools` tinyint(1) NOT NULL DEFAULT 0,
  `editor_roles` varchar(255) NOT NULL DEFAULT '',
  `mysql_table_name` text NOT NULL DEFAULT '',
  `edit_only_own_rows` tinyint(1) NOT NULL DEFAULT 0,
  `userid_column_id` int(11) NOT NULL DEFAULT 0,
  `display_length` int(3) NOT NULL DEFAULT 10,
  `auto_refresh` int(3) NOT NULL DEFAULT 0,
  `fixed_columns` tinyint(1) NOT NULL DEFAULT -1,
  `fixed_layout` tinyint(1) NOT NULL DEFAULT 0,
  `responsive` tinyint(1) NOT NULL DEFAULT 0,
  `scrollable` tinyint(1) NOT NULL DEFAULT 0,
  `word_wrap` tinyint(1) NOT NULL DEFAULT 0,
  `hide_before_load` tinyint(1) NOT NULL DEFAULT 0,
  `var1` varchar(255) NOT NULL DEFAULT '',
  `var2` varchar(255) NOT NULL DEFAULT '',
  `var3` varchar(255) NOT NULL DEFAULT '',
  `var4` varchar(255) NOT NULL DEFAULT '',
  `var5` varchar(255) NOT NULL DEFAULT '',
  `var6` varchar(255) NOT NULL DEFAULT '',
  `var7` varchar(255) NOT NULL DEFAULT '',
  `var8` varchar(255) NOT NULL DEFAULT '',
  `var9` varchar(255) NOT NULL DEFAULT '',
  `tabletools_config` varchar(255) NOT NULL DEFAULT '',
  `advanced_settings` text NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wpdatatables_columns`
--

CREATE TABLE `wp_784555_wpdatatables_columns` (
  `id` bigint(20) NOT NULL,
  `table_id` bigint(20) NOT NULL,
  `orig_header` varchar(255) NOT NULL,
  `display_header` varchar(255) NOT NULL,
  `filter_type` enum('none','null_str','text','number','number-range','date-range','datetime-range','time-range','select','multiselect','checkbox') NOT NULL,
  `column_type` enum('autodetect','string','int','float','date','link','email','image','formula','datetime','time','masterdetail') NOT NULL,
  `input_type` enum('none','text','textarea','mce-editor','date','datetime','time','link','email','selectbox','multi-selectbox','attachment') NOT NULL DEFAULT 'text',
  `input_mandatory` tinyint(1) NOT NULL DEFAULT 0,
  `id_column` tinyint(1) NOT NULL DEFAULT 0,
  `group_column` tinyint(1) NOT NULL DEFAULT 0,
  `sort_column` tinyint(1) NOT NULL DEFAULT 0,
  `hide_on_phones` tinyint(1) NOT NULL DEFAULT 0,
  `hide_on_tablets` tinyint(1) NOT NULL DEFAULT 0,
  `visible` tinyint(1) NOT NULL DEFAULT 1,
  `sum_column` tinyint(1) NOT NULL DEFAULT 0,
  `skip_thousands_separator` tinyint(1) NOT NULL DEFAULT 0,
  `width` varchar(4) NOT NULL DEFAULT '',
  `possible_values` text NOT NULL DEFAULT '',
  `default_value` text NOT NULL DEFAULT '',
  `css_class` varchar(255) NOT NULL DEFAULT '',
  `text_before` varchar(255) NOT NULL DEFAULT '',
  `text_after` varchar(255) NOT NULL DEFAULT '',
  `formatting_rules` text NOT NULL DEFAULT '',
  `calc_formula` text NOT NULL DEFAULT '',
  `color` varchar(255) NOT NULL DEFAULT '',
  `advanced_settings` text NOT NULL DEFAULT '',
  `pos` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wpdatatables_rows`
--

CREATE TABLE `wp_784555_wpdatatables_rows` (
  `id` bigint(20) NOT NULL,
  `table_id` bigint(20) NOT NULL,
  `data` text NOT NULL DEFAULT ''
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wpdatatable_6`
--

CREATE TABLE `wp_784555_wpdatatable_6` (
  `wdt_ID` int(11) NOT NULL,
  `nouvellecolonne` varchar(255) DEFAULT NULL,
  `nouvellecolonne1` varchar(255) DEFAULT NULL,
  `nouvellecolonne2` varchar(2000) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wpfm_backup`
--

CREATE TABLE `wp_784555_wpfm_backup` (
  `id` int(11) NOT NULL,
  `backup_name` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `backup_date` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wpforms_entries`
--

CREATE TABLE `wp_784555_wpforms_entries` (
  `entry_id` bigint(20) NOT NULL,
  `form_id` bigint(20) NOT NULL,
  `post_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `status` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL,
  `viewed` tinyint(1) DEFAULT 0,
  `starred` tinyint(1) DEFAULT 0,
  `fields` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `meta` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  `ip_address` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_agent` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_uuid` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wpforms_entry_fields`
--

CREATE TABLE `wp_784555_wpforms_entry_fields` (
  `id` bigint(20) NOT NULL,
  `entry_id` bigint(20) NOT NULL,
  `form_id` bigint(20) NOT NULL,
  `field_id` int(11) NOT NULL,
  `value` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wpforms_entry_meta`
--

CREATE TABLE `wp_784555_wpforms_entry_meta` (
  `id` bigint(20) NOT NULL,
  `entry_id` bigint(20) NOT NULL,
  `form_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `status` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL,
  `data` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wpforms_tasks_meta`
--

CREATE TABLE `wp_784555_wpforms_tasks_meta` (
  `id` bigint(20) NOT NULL,
  `action` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `data` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wpmailsmtp_debug_events`
--

CREATE TABLE `wp_784555_wpmailsmtp_debug_events` (
  `id` int(10) UNSIGNED NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `initiator` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `event_type` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_wpmailsmtp_tasks_meta`
--

CREATE TABLE `wp_784555_wpmailsmtp_tasks_meta` (
  `id` bigint(20) NOT NULL,
  `action` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `data` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_yoast_indexable`
--

CREATE TABLE `wp_784555_yoast_indexable` (
  `id` int(11) UNSIGNED NOT NULL,
  `permalink` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `permalink_hash` varchar(40) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `object_id` bigint(20) DEFAULT NULL,
  `object_type` varchar(32) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `object_sub_type` varchar(32) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `author_id` bigint(20) DEFAULT NULL,
  `post_parent` bigint(20) DEFAULT NULL,
  `title` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `description` mediumtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `breadcrumb_title` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `post_status` varchar(20) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT NULL,
  `is_protected` tinyint(1) DEFAULT 0,
  `has_public_posts` tinyint(1) DEFAULT NULL,
  `number_of_pages` int(11) UNSIGNED DEFAULT NULL,
  `canonical` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `primary_focus_keyword` varchar(191) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `primary_focus_keyword_score` int(3) DEFAULT NULL,
  `readability_score` int(3) DEFAULT NULL,
  `is_cornerstone` tinyint(1) DEFAULT 0,
  `is_robots_noindex` tinyint(1) DEFAULT 0,
  `is_robots_nofollow` tinyint(1) DEFAULT 0,
  `is_robots_noarchive` tinyint(1) DEFAULT 0,
  `is_robots_noimageindex` tinyint(1) DEFAULT 0,
  `is_robots_nosnippet` tinyint(1) DEFAULT 0,
  `twitter_title` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `twitter_image` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `twitter_description` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `twitter_image_id` varchar(191) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `twitter_image_source` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `open_graph_title` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `open_graph_description` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `open_graph_image` longtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `open_graph_image_id` varchar(191) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `open_graph_image_source` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `open_graph_image_meta` mediumtext COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `link_count` int(11) DEFAULT NULL,
  `incoming_link_count` int(11) DEFAULT NULL,
  `prominent_words_version` int(11) UNSIGNED DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `blog_id` bigint(20) NOT NULL DEFAULT 1,
  `language` varchar(32) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `region` varchar(32) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `schema_page_type` varchar(64) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `schema_article_type` varchar(64) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `has_ancestors` tinyint(1) DEFAULT 0,
  `estimated_reading_time_minutes` int(11) DEFAULT NULL,
  `version` int(11) DEFAULT 1,
  `object_last_modified` datetime DEFAULT NULL,
  `object_published_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_yoast_indexable_hierarchy`
--

CREATE TABLE `wp_784555_yoast_indexable_hierarchy` (
  `indexable_id` int(11) UNSIGNED NOT NULL,
  `ancestor_id` int(11) UNSIGNED NOT NULL,
  `depth` int(11) UNSIGNED DEFAULT NULL,
  `blog_id` bigint(20) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;



-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_yoast_migrations`
--

CREATE TABLE `wp_784555_yoast_migrations` (
  `id` int(11) UNSIGNED NOT NULL,
  `version` varchar(191) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;


-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_yoast_primary_term`
--

CREATE TABLE `wp_784555_yoast_primary_term` (
  `id` int(11) UNSIGNED NOT NULL,
  `post_id` bigint(20) DEFAULT NULL,
  `term_id` bigint(20) DEFAULT NULL,
  `taxonomy` varchar(32) COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `blog_id` bigint(20) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `wp_784555_yoast_seo_links`
--

CREATE TABLE `wp_784555_yoast_seo_links` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `url` varchar(255) DEFAULT NULL,
  `post_id` bigint(20) UNSIGNED DEFAULT NULL,
  `target_post_id` bigint(20) UNSIGNED DEFAULT NULL,
  `type` varchar(8) DEFAULT NULL,
  `indexable_id` int(11) UNSIGNED DEFAULT NULL,
  `target_indexable_id` int(11) UNSIGNED DEFAULT NULL,
  `height` int(11) UNSIGNED DEFAULT NULL,
  `width` int(11) UNSIGNED DEFAULT NULL,
  `size` int(11) UNSIGNED DEFAULT NULL,
  `language` varchar(32) DEFAULT NULL,
  `region` varchar(32) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure de la table `_mig_queue_failures`
--

CREATE TABLE `_mig_queue_failures` (
  `id` bigint(20) NOT NULL,
  `job` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `error` text COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `failed_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `_mig_queue_jobs`
--

CREATE TABLE `_mig_queue_jobs` (
  `id` bigint(20) NOT NULL,
  `job` longtext COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `attempts` tinyint(3) NOT NULL DEFAULT 0,
  `reserved_at` datetime DEFAULT NULL,
  `available_at` datetime NOT NULL,
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;

-- --------------------------------------------------------

--
-- Structure de la table `_mig_wpmdb_alter_statements`
--

CREATE TABLE `_mig_wpmdb_alter_statements` (
  `query` longtext NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Index pour les tables déchargées
--

--
-- Index pour la table `lmczp_actionscheduler_actions`
--
ALTER TABLE `lmczp_actionscheduler_actions`
  ADD PRIMARY KEY (`action_id`),
  ADD KEY `hook` (`hook`),
  ADD KEY `status` (`status`),
  ADD KEY `scheduled_date_gmt` (`scheduled_date_gmt`),
  ADD KEY `args` (`args`),
  ADD KEY `group_id` (`group_id`),
  ADD KEY `last_attempt_gmt` (`last_attempt_gmt`),
  ADD KEY `claim_id_status_scheduled_date_gmt` (`claim_id`,`status`,`scheduled_date_gmt`);

--
-- Index pour la table `lmczp_actionscheduler_claims`
--
ALTER TABLE `lmczp_actionscheduler_claims`
  ADD PRIMARY KEY (`claim_id`),
  ADD KEY `date_created_gmt` (`date_created_gmt`);

--
-- Index pour la table `lmczp_actionscheduler_groups`
--
ALTER TABLE `lmczp_actionscheduler_groups`
  ADD PRIMARY KEY (`group_id`),
  ADD KEY `slug` (`slug`(191));

--
-- Index pour la table `lmczp_actionscheduler_logs`
--
ALTER TABLE `lmczp_actionscheduler_logs`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `action_id` (`action_id`),
  ADD KEY `log_date_gmt` (`log_date_gmt`);

--
-- Index pour la table `lmczp_ac_segments`
--
ALTER TABLE `lmczp_ac_segments`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_admin_columns`
--
ALTER TABLE `lmczp_admin_columns`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `list_id` (`list_id`);

--
-- Index pour la table `lmczp_aiowps_audit_log`
--
ALTER TABLE `lmczp_aiowps_audit_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `username` (`username`),
  ADD KEY `ip` (`ip`),
  ADD KEY `level` (`level`),
  ADD KEY `event_type` (`event_type`);

--
-- Index pour la table `lmczp_aiowps_debug_log`
--
ALTER TABLE `lmczp_aiowps_debug_log`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_aiowps_events`
--
ALTER TABLE `lmczp_aiowps_events`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_aiowps_global_meta`
--
ALTER TABLE `lmczp_aiowps_global_meta`
  ADD PRIMARY KEY (`meta_id`);

--
-- Index pour la table `lmczp_aiowps_logged_in_users`
--
ALTER TABLE `lmczp_aiowps_logged_in_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_id` (`user_id`),
  ADD KEY `created` (`created`),
  ADD KEY `expires` (`expires`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `site_id` (`site_id`);

--
-- Index pour la table `lmczp_aiowps_login_lockdown`
--
ALTER TABLE `lmczp_aiowps_login_lockdown`
  ADD PRIMARY KEY (`id`),
  ADD KEY `failed_login_ip` (`failed_login_ip`),
  ADD KEY `is_lockout_email_sent` (`is_lockout_email_sent`),
  ADD KEY `unlock_key` (`unlock_key`);

--
-- Index pour la table `lmczp_aiowps_message_store`
--
ALTER TABLE `lmczp_aiowps_message_store`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_aiowps_permanent_block`
--
ALTER TABLE `lmczp_aiowps_permanent_block`
  ADD PRIMARY KEY (`id`),
  ADD KEY `blocked_ip` (`blocked_ip`);

--
-- Index pour la table `lmczp_commentmeta`
--
ALTER TABLE `lmczp_commentmeta`
  ADD PRIMARY KEY (`meta_id`),
  ADD KEY `comment_id` (`comment_id`),
  ADD KEY `meta_key` (`meta_key`(191));

--
-- Index pour la table `lmczp_comments`
--
ALTER TABLE `lmczp_comments`
  ADD PRIMARY KEY (`comment_ID`),
  ADD KEY `comment_post_ID` (`comment_post_ID`),
  ADD KEY `comment_approved_date_gmt` (`comment_approved`,`comment_date_gmt`),
  ADD KEY `comment_date_gmt` (`comment_date_gmt`),
  ADD KEY `comment_parent` (`comment_parent`),
  ADD KEY `comment_author_email` (`comment_author_email`(10));

--
-- Index pour la table `lmczp_e_events`
--
ALTER TABLE `lmczp_e_events`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_at_index` (`created_at`);

--
-- Index pour la table `lmczp_jet_post_types`
--
ALTER TABLE `lmczp_jet_post_types`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_jet_taxonomies`
--
ALTER TABLE `lmczp_jet_taxonomies`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_links`
--
ALTER TABLE `lmczp_links`
  ADD PRIMARY KEY (`link_id`),
  ADD KEY `link_visible` (`link_visible`);

--
-- Index pour la table `lmczp_mapsvg6_maps`
--
ALTER TABLE `lmczp_mapsvg6_maps`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_mapsvg6_objects_0`
--
ALTER TABLE `lmczp_mapsvg6_objects_0`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_mapsvg6_objects_1`
--
ALTER TABLE `lmczp_mapsvg6_objects_1`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_mapsvg6_objects_2`
--
ALTER TABLE `lmczp_mapsvg6_objects_2`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_mapsvg6_objects_3`
--
ALTER TABLE `lmczp_mapsvg6_objects_3`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_mapsvg6_objects_4`
--
ALTER TABLE `lmczp_mapsvg6_objects_4`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_mapsvg6_r2o`
--
ALTER TABLE `lmczp_mapsvg6_r2o`
  ADD KEY `objects_table` (`objects_table`,`regions_table`,`region_id`);

--
-- Index pour la table `lmczp_mapsvg6_regions_0`
--
ALTER TABLE `lmczp_mapsvg6_regions_0`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `lmczp_mapsvg6_regions_0` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `lmczp_mapsvg6_regions_1`
--
ALTER TABLE `lmczp_mapsvg6_regions_1`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `lmczp_mapsvg6_regions_1` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `lmczp_mapsvg6_regions_2`
--
ALTER TABLE `lmczp_mapsvg6_regions_2`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `lmczp_mapsvg6_regions_2` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `lmczp_mapsvg6_regions_3`
--
ALTER TABLE `lmczp_mapsvg6_regions_3`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `lmczp_mapsvg6_regions_3` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `lmczp_mapsvg6_regions_4`
--
ALTER TABLE `lmczp_mapsvg6_regions_4`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `lmczp_mapsvg6_regions_4` ADD FULLTEXT KEY `_keywords` (`id`);

--
-- Index pour la table `lmczp_mapsvg6_regions_5`
--
ALTER TABLE `lmczp_mapsvg6_regions_5`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `lmczp_mapsvg6_regions_5` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `lmczp_mapsvg6_schema`
--
ALTER TABLE `lmczp_mapsvg6_schema`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_mapsvg6_settings`
--
ALTER TABLE `lmczp_mapsvg6_settings`
  ADD PRIMARY KEY (`key`);

--
-- Index pour la table `lmczp_mec_attendees`
--
ALTER TABLE `lmczp_mec_attendees`
  ADD PRIMARY KEY (`attendee_id`);

--
-- Index pour la table `lmczp_mec_bookings`
--
ALTER TABLE `lmczp_mec_bookings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `event_id` (`event_id`,`ticket_ids`,`status`,`confirmed`,`verified`,`date`),
  ADD KEY `booking_id` (`booking_id`),
  ADD KEY `timestamp` (`timestamp`),
  ADD KEY `user_id` (`user_id`);

--
-- Index pour la table `lmczp_mec_dates`
--
ALTER TABLE `lmczp_mec_dates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `post_id` (`post_id`),
  ADD KEY `tstart` (`tstart`),
  ADD KEY `tend` (`tend`);

--
-- Index pour la table `lmczp_mec_events`
--
ALTER TABLE `lmczp_mec_events`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `ID` (`id`),
  ADD UNIQUE KEY `post_id` (`post_id`),
  ADD KEY `start` (`start`,`end`,`repeat`,`rinterval`,`year`,`month`,`day`,`week`,`weekday`,`weekdays`,`time_start`,`time_end`);

--
-- Index pour la table `lmczp_mec_occurrences`
--
ALTER TABLE `lmczp_mec_occurrences`
  ADD PRIMARY KEY (`id`),
  ADD KEY `post_id` (`post_id`),
  ADD KEY `occurrence` (`occurrence`);

--
-- Index pour la table `lmczp_mec_users`
--
ALTER TABLE `lmczp_mec_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Index pour la table `lmczp_options`
--
ALTER TABLE `lmczp_options`
  ADD PRIMARY KEY (`option_id`),
  ADD UNIQUE KEY `option_name` (`option_name`),
  ADD KEY `autoload` (`autoload`);

--
-- Index pour la table `lmczp_pacad_documents`
--
ALTER TABLE `lmczp_pacad_documents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pacad_documents_post_id_foreign` (`post_id`),
  ADD KEY `pacad_documents_event_id_foreign` (`event_id`),
  ADD KEY `pacad_documents_author_id_foreign` (`author_id`);

--
-- Index pour la table `lmczp_pacad_document_effects`
--
ALTER TABLE `lmczp_pacad_document_effects`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pacad_document_effects_document_id_foreign` (`document_id`);

--
-- Index pour la table `lmczp_pacad_document_tags`
--
ALTER TABLE `lmczp_pacad_document_tags`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pacad_document_tags_document_id_foreign` (`document_id`),
  ADD KEY `pacad_document_tags_tag_id_foreign` (`tag_id`);

--
-- Index pour la table `lmczp_pacad_document_zones`
--
ALTER TABLE `lmczp_pacad_document_zones`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pacad_document_zones_document_id_foreign` (`document_id`),
  ADD KEY `pacad_document_zones_region_id_foreign` (`region_id`),
  ADD KEY `pacad_document_zones_department_id_foreign` (`department_id`),
  ADD KEY `pacad_document_zones_commune_id_foreign` (`commune_id`);

--
-- Index pour la table `lmczp_pacad_donation_extensions`
--
ALTER TABLE `lmczp_pacad_donation_extensions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `wp_pacad_donation_extensions_donation_id_foreign` (`donation_id`),
  ADD KEY `wp_pacad_donation_extensions_revision_author_id_foreign` (`revision_author_id`);

--
-- Index pour la table `lmczp_pacad_pcc_indicators_data`
--
ALTER TABLE `lmczp_pacad_pcc_indicators_data`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pacad_pcc_indicators_data_pcc_indicator_id_foreign` (`pcc_indicator_id`),
  ADD KEY `pacad_pcc_indicators_data_year_index` (`year`);

--
-- Index pour la table `lmczp_pacad_projects`
--
ALTER TABLE `lmczp_pacad_projects`
  ADD PRIMARY KEY (`id`),
  ADD KEY `wp_pacad_projects_post_id_foreign` (`post_id`);

--
-- Index pour la table `lmczp_pacad_project_agencies`
--
ALTER TABLE `lmczp_pacad_project_agencies`
  ADD PRIMARY KEY (`id`),
  ADD KEY `wp_pacad_project_agencies_project_id_foreign` (`project_id`),
  ADD KEY `wp_pacad_project_agencies_agency_id_foreign` (`agency_id`),
  ADD KEY `wp_pacad_project_agencies_focal_point_id_foreign` (`focal_point_id`),
  ADD KEY `wp_pacad_project_agencies_revision_author_id_foreign` (`revision_author_id`);

--
-- Index pour la table `lmczp_pacad_project_donations`
--
ALTER TABLE `lmczp_pacad_project_donations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `wp_pacad_project_donations_project_id_foreign` (`project_id`),
  ADD KEY `wp_pacad_project_donations_donor_id_foreign` (`donor_id`),
  ADD KEY `wp_pacad_project_donations_revision_author_id_foreign` (`revision_author_id`);

--
-- Index pour la table `lmczp_pacad_project_donation_budgets`
--
ALTER TABLE `lmczp_pacad_project_donation_budgets`
  ADD PRIMARY KEY (`id`),
  ADD KEY `wp_pacad_project_donation_budgets_project_id_foreign` (`project_id`),
  ADD KEY `wp_pacad_project_donation_budgets_donation_id_foreign` (`donation_id`),
  ADD KEY `wp_pacad_project_donation_budgets_sdg_id_foreign` (`sdg_id`),
  ADD KEY `wp_pacad_project_donation_budgets_revision_author_id_foreign` (`revision_author_id`);

--
-- Index pour la table `lmczp_pacad_project_effects`
--
ALTER TABLE `lmczp_pacad_project_effects`
  ADD PRIMARY KEY (`id`),
  ADD KEY `wp_pacad_project_effects_project_id_foreign` (`project_id`),
  ADD KEY `wp_pacad_project_effects_revision_author_id_foreign` (`revision_author_id`);

--
-- Index pour la table `lmczp_pacad_project_extensions`
--
ALTER TABLE `lmczp_pacad_project_extensions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `wp_pacad_project_extensions_project_id_foreign` (`project_id`),
  ADD KEY `wp_pacad_project_extensions_revision_author_id_foreign` (`revision_author_id`);

--
-- Index pour la table `lmczp_pacad_project_fundings`
--
ALTER TABLE `lmczp_pacad_project_fundings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `wp_pacad_project_fundings_project_id_foreign` (`project_id`),
  ADD KEY `wp_pacad_project_fundings_agency_id_foreign` (`agency_id`),
  ADD KEY `wp_pacad_project_fundings_revision_author_id_foreign` (`revision_author_id`);

--
-- Index pour la table `lmczp_pacad_project_info`
--
ALTER TABLE `lmczp_pacad_project_info`
  ADD PRIMARY KEY (`id`),
  ADD KEY `wp_pacad_project_info_project_id_foreign` (`project_id`),
  ADD KEY `wp_pacad_project_info_revision_author_id_foreign` (`revision_author_id`);

--
-- Index pour la table `lmczp_pacad_project_zones`
--
ALTER TABLE `lmczp_pacad_project_zones`
  ADD PRIMARY KEY (`id`),
  ADD KEY `wp_pacad_project_zones_project_id_foreign` (`project_id`),
  ADD KEY `wp_pacad_project_zones_region_id_foreign` (`region_id`),
  ADD KEY `wp_pacad_project_zones_department_id_foreign` (`department_id`),
  ADD KEY `wp_pacad_project_zones_commune_id_foreign` (`commune_id`),
  ADD KEY `wp_pacad_project_zones_revision_author_id_foreign` (`revision_author_id`);

--
-- Index pour la table `lmczp_pacad_sms_alerts_reports`
--
ALTER TABLE `lmczp_pacad_sms_alerts_reports`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pacad_sms_alerts_reports_sent_by_foreign` (`sent_by`);

--
-- Index pour la table `lmczp_pacad_sms_alerts_report_data`
--
ALTER TABLE `lmczp_pacad_sms_alerts_report_data`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_postmeta`
--
ALTER TABLE `lmczp_postmeta`
  ADD PRIMARY KEY (`meta_id`),
  ADD KEY `post_id` (`post_id`),
  ADD KEY `meta_key` (`meta_key`(191));

--
-- Index pour la table `lmczp_posts`
--
ALTER TABLE `lmczp_posts`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `post_name` (`post_name`(191)),
  ADD KEY `type_status_date` (`post_type`,`post_status`,`post_date`,`ID`),
  ADD KEY `post_parent` (`post_parent`),
  ADD KEY `post_author` (`post_author`);

--
-- Index pour la table `lmczp_rank_math_internal_links`
--
ALTER TABLE `lmczp_rank_math_internal_links`
  ADD PRIMARY KEY (`id`),
  ADD KEY `link_direction` (`post_id`,`type`),
  ADD KEY `target_post_id` (`target_post_id`);

--
-- Index pour la table `lmczp_rank_math_internal_meta`
--
ALTER TABLE `lmczp_rank_math_internal_meta`
  ADD PRIMARY KEY (`object_id`);

--
-- Index pour la table `lmczp_snippets`
--
ALTER TABLE `lmczp_snippets`
  ADD PRIMARY KEY (`id`),
  ADD KEY `scope` (`scope`),
  ADD KEY `active` (`active`);

--
-- Index pour la table `lmczp_tec_events`
--
ALTER TABLE `lmczp_tec_events`
  ADD PRIMARY KEY (`event_id`),
  ADD UNIQUE KEY `post_id` (`post_id`);

--
-- Index pour la table `lmczp_tec_occurrences`
--
ALTER TABLE `lmczp_tec_occurrences`
  ADD PRIMARY KEY (`occurrence_id`),
  ADD UNIQUE KEY `hash` (`hash`),
  ADD KEY `event_id` (`event_id`);

--
-- Index pour la table `lmczp_tec_series_relationships`
--
ALTER TABLE `lmczp_tec_series_relationships`
  ADD PRIMARY KEY (`relationship_id`),
  ADD KEY `series_post_id` (`series_post_id`),
  ADD KEY `event_post_id` (`event_post_id`);

--
-- Index pour la table `lmczp_termmeta`
--
ALTER TABLE `lmczp_termmeta`
  ADD PRIMARY KEY (`meta_id`),
  ADD KEY `term_id` (`term_id`),
  ADD KEY `meta_key` (`meta_key`(191));

--
-- Index pour la table `lmczp_terms`
--
ALTER TABLE `lmczp_terms`
  ADD PRIMARY KEY (`term_id`),
  ADD KEY `slug` (`slug`(191)),
  ADD KEY `name` (`name`(191));

--
-- Index pour la table `lmczp_term_relationships`
--
ALTER TABLE `lmczp_term_relationships`
  ADD PRIMARY KEY (`object_id`,`term_taxonomy_id`),
  ADD KEY `term_taxonomy_id` (`term_taxonomy_id`);

--
-- Index pour la table `lmczp_term_taxonomy`
--
ALTER TABLE `lmczp_term_taxonomy`
  ADD PRIMARY KEY (`term_taxonomy_id`),
  ADD UNIQUE KEY `term_id_taxonomy` (`term_id`,`taxonomy`),
  ADD KEY `taxonomy` (`taxonomy`);

--
-- Index pour la table `lmczp_um_metadata`
--
ALTER TABLE `lmczp_um_metadata`
  ADD PRIMARY KEY (`umeta_id`),
  ADD KEY `user_id_indx` (`user_id`),
  ADD KEY `meta_key_indx` (`um_key`),
  ADD KEY `meta_value_indx` (`um_value`(191));

--
-- Index pour la table `lmczp_usermeta`
--
ALTER TABLE `lmczp_usermeta`
  ADD PRIMARY KEY (`umeta_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `meta_key` (`meta_key`(191));

--
-- Index pour la table `lmczp_users`
--
ALTER TABLE `lmczp_users`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `user_login_key` (`user_login`),
  ADD KEY `user_nicename` (`user_nicename`),
  ADD KEY `user_email` (`user_email`);

--
-- Index pour la table `lmczp_wpda_org_chart_popup_theme`
--
ALTER TABLE `lmczp_wpda_org_chart_popup_theme`
  ADD UNIQUE KEY `id` (`id`);

--
-- Index pour la table `lmczp_wpda_org_chart_theme`
--
ALTER TABLE `lmczp_wpda_org_chart_theme`
  ADD UNIQUE KEY `id` (`id`);

--
-- Index pour la table `lmczp_wpda_org_chart_tree`
--
ALTER TABLE `lmczp_wpda_org_chart_tree`
  ADD UNIQUE KEY `id` (`id`);

--
-- Index pour la table `lmczp_wpforms_entries`
--
ALTER TABLE `lmczp_wpforms_entries`
  ADD PRIMARY KEY (`entry_id`),
  ADD KEY `form_id` (`form_id`);

--
-- Index pour la table `lmczp_wpforms_entry_fields`
--
ALTER TABLE `lmczp_wpforms_entry_fields`
  ADD PRIMARY KEY (`id`),
  ADD KEY `entry_id` (`entry_id`),
  ADD KEY `form_id` (`form_id`),
  ADD KEY `field_id` (`field_id`);

--
-- Index pour la table `lmczp_wpforms_entry_meta`
--
ALTER TABLE `lmczp_wpforms_entry_meta`
  ADD PRIMARY KEY (`id`),
  ADD KEY `entry_id` (`entry_id`);

--
-- Index pour la table `lmczp_wpforms_tasks_meta`
--
ALTER TABLE `lmczp_wpforms_tasks_meta`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_wpie_template`
--
ALTER TABLE `lmczp_wpie_template`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_wpinventory_category`
--
ALTER TABLE `lmczp_wpinventory_category`
  ADD PRIMARY KEY (`category_id`);

--
-- Index pour la table `lmczp_wpinventory_image`
--
ALTER TABLE `lmczp_wpinventory_image`
  ADD PRIMARY KEY (`image_id`),
  ADD KEY `inventory_id` (`inventory_id`),
  ADD KEY `post_id` (`post_id`);

--
-- Index pour la table `lmczp_wpinventory_item`
--
ALTER TABLE `lmczp_wpinventory_item`
  ADD PRIMARY KEY (`inventory_id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `inventory_status` (`inventory_status`);

--
-- Index pour la table `lmczp_wpinventory_label`
--
ALTER TABLE `lmczp_wpinventory_label`
  ADD PRIMARY KEY (`label_id`);

--
-- Index pour la table `lmczp_wpinventory_media`
--
ALTER TABLE `lmczp_wpinventory_media`
  ADD PRIMARY KEY (`media_id`),
  ADD KEY `inventory_id` (`inventory_id`);

--
-- Index pour la table `lmczp_wpinventory_reservation`
--
ALTER TABLE `lmczp_wpinventory_reservation`
  ADD PRIMARY KEY (`reservation_id`);

--
-- Index pour la table `lmczp_wpinventory_reservation_item`
--
ALTER TABLE `lmczp_wpinventory_reservation_item`
  ADD PRIMARY KEY (`reservation_item_id`),
  ADD KEY `inventory_id` (`inventory_id`),
  ADD KEY `reservation_id` (`reservation_id`);

--
-- Index pour la table `lmczp_wpinventory_status`
--
ALTER TABLE `lmczp_wpinventory_status`
  ADD PRIMARY KEY (`status_id`);

--
-- Index pour la table `lmczp_wpmailsmtp_debug_events`
--
ALTER TABLE `lmczp_wpmailsmtp_debug_events`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `lmczp_wpmailsmtp_tasks_meta`
--
ALTER TABLE `lmczp_wpmailsmtp_tasks_meta`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_actionscheduler_actions`
--
ALTER TABLE `wp_784555_actionscheduler_actions`
  ADD PRIMARY KEY (`action_id`),
  ADD KEY `hook` (`hook`),
  ADD KEY `status` (`status`),
  ADD KEY `scheduled_date_gmt` (`scheduled_date_gmt`),
  ADD KEY `args` (`args`),
  ADD KEY `group_id` (`group_id`),
  ADD KEY `last_attempt_gmt` (`last_attempt_gmt`),
  ADD KEY `claim_id` (`claim_id`),
  ADD KEY `claim_id_status_scheduled_date_gmt` (`claim_id`,`status`,`scheduled_date_gmt`);

--
-- Index pour la table `wp_784555_actionscheduler_claims`
--
ALTER TABLE `wp_784555_actionscheduler_claims`
  ADD PRIMARY KEY (`claim_id`),
  ADD KEY `date_created_gmt` (`date_created_gmt`);

--
-- Index pour la table `wp_784555_actionscheduler_groups`
--
ALTER TABLE `wp_784555_actionscheduler_groups`
  ADD PRIMARY KEY (`group_id`),
  ADD KEY `slug` (`slug`(191));

--
-- Index pour la table `wp_784555_actionscheduler_logs`
--
ALTER TABLE `wp_784555_actionscheduler_logs`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `action_id` (`action_id`),
  ADD KEY `log_date_gmt` (`log_date_gmt`);

--
-- Index pour la table `wp_784555_acym_action`
--
ALTER TABLE `wp_784555_acym_action`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_wp_784555_acym_action1` (`condition_id`);

--
-- Index pour la table `wp_784555_acym_automation`
--
ALTER TABLE `wp_784555_acym_automation`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_acym_campaign`
--
ALTER TABLE `wp_784555_acym_campaign`
  ADD PRIMARY KEY (`id`),
  ADD KEY `index_wp_784555_acym_campaign_has_mail1` (`mail_id`);

--
-- Index pour la table `wp_784555_acym_condition`
--
ALTER TABLE `wp_784555_acym_condition`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_wp_784555_acym_condition1` (`step_id`);

--
-- Index pour la table `wp_784555_acym_configuration`
--
ALTER TABLE `wp_784555_acym_configuration`
  ADD PRIMARY KEY (`name`);

--
-- Index pour la table `wp_784555_acym_custom_zone`
--
ALTER TABLE `wp_784555_acym_custom_zone`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_acym_field`
--
ALTER TABLE `wp_784555_acym_field`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_acym_followup`
--
ALTER TABLE `wp_784555_acym_followup`
  ADD PRIMARY KEY (`id`),
  ADD KEY `index_wp_784555_acym_followup_has_list` (`list_id`);

--
-- Index pour la table `wp_784555_acym_followup_has_mail`
--
ALTER TABLE `wp_784555_acym_followup_has_mail`
  ADD PRIMARY KEY (`mail_id`,`followup_id`),
  ADD KEY `index_wp_784555_acym_mail_has_followup1` (`followup_id`),
  ADD KEY `index_wp_784555_acym_mail_has_followup2` (`mail_id`);

--
-- Index pour la table `wp_784555_acym_form`
--
ALTER TABLE `wp_784555_acym_form`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_acym_history`
--
ALTER TABLE `wp_784555_acym_history`
  ADD PRIMARY KEY (`user_id`,`date`);

--
-- Index pour la table `wp_784555_acym_list`
--
ALTER TABLE `wp_784555_acym_list`
  ADD PRIMARY KEY (`id`),
  ADD KEY `index_wp_784555_acym_list_has_mail1` (`welcome_id`),
  ADD KEY `index_wp_784555_acym_list_has_mail2` (`unsubscribe_id`);

--
-- Index pour la table `wp_784555_acym_mail`
--
ALTER TABLE `wp_784555_acym_mail`
  ADD PRIMARY KEY (`id`),
  ADD KEY `index_wp_784555_acym_mail1` (`parent_id`);

--
-- Index pour la table `wp_784555_acym_mail_has_list`
--
ALTER TABLE `wp_784555_acym_mail_has_list`
  ADD PRIMARY KEY (`mail_id`,`list_id`),
  ADD KEY `index_wp_784555_acym_mail_has_list1` (`list_id`),
  ADD KEY `index_wp_784555_acym_mail_has_list2` (`mail_id`);

--
-- Index pour la table `wp_784555_acym_mail_override`
--
ALTER TABLE `wp_784555_acym_mail_override`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_wp_784555_acym_mail_override1` (`mail_id`);

--
-- Index pour la table `wp_784555_acym_mail_stat`
--
ALTER TABLE `wp_784555_acym_mail_stat`
  ADD PRIMARY KEY (`mail_id`);

--
-- Index pour la table `wp_784555_acym_plugin`
--
ALTER TABLE `wp_784555_acym_plugin`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_acym_queue`
--
ALTER TABLE `wp_784555_acym_queue`
  ADD PRIMARY KEY (`mail_id`,`user_id`),
  ADD KEY `index_wp_784555_acym_queue1` (`mail_id`),
  ADD KEY `index_wp_784555_acym_queue2` (`user_id`);

--
-- Index pour la table `wp_784555_acym_rule`
--
ALTER TABLE `wp_784555_acym_rule`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_acym_segment`
--
ALTER TABLE `wp_784555_acym_segment`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_acym_step`
--
ALTER TABLE `wp_784555_acym_step`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_wp_784555_acym__step1` (`automation_id`);

--
-- Index pour la table `wp_784555_acym_tag`
--
ALTER TABLE `wp_784555_acym_tag`
  ADD PRIMARY KEY (`name`,`type`,`id_element`);

--
-- Index pour la table `wp_784555_acym_url`
--
ALTER TABLE `wp_784555_acym_url`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_acym_url_click`
--
ALTER TABLE `wp_784555_acym_url_click`
  ADD PRIMARY KEY (`mail_id`,`url_id`,`user_id`),
  ADD KEY `index_wp_784555_acym_url_has_url1` (`url_id`);

--
-- Index pour la table `wp_784555_acym_user`
--
ALTER TABLE `wp_784555_acym_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email_UNIQUE` (`email`);

--
-- Index pour la table `wp_784555_acym_user_has_field`
--
ALTER TABLE `wp_784555_acym_user_has_field`
  ADD PRIMARY KEY (`user_id`,`field_id`),
  ADD KEY `index_wp_784555_acym_user_has_field1` (`field_id`),
  ADD KEY `index_wp_784555_acym_user_has_field2` (`user_id`);

--
-- Index pour la table `wp_784555_acym_user_has_list`
--
ALTER TABLE `wp_784555_acym_user_has_list`
  ADD PRIMARY KEY (`user_id`,`list_id`),
  ADD KEY `index_wp_784555_acym_user_has_list1` (`list_id`),
  ADD KEY `index_wp_784555_acym_user_has_list2` (`user_id`),
  ADD KEY `index_wp_784555_acym_user_has_list3` (`subscription_date`),
  ADD KEY `index_wp_784555_acym_user_has_list4` (`unsubscribe_date`);

--
-- Index pour la table `wp_784555_acym_user_stat`
--
ALTER TABLE `wp_784555_acym_user_stat`
  ADD PRIMARY KEY (`user_id`,`mail_id`),
  ADD KEY `fk_wp_784555_acym_user_stat1` (`mail_id`);

--
-- Index pour la table `wp_784555_aryo_activity_log`
--
ALTER TABLE `wp_784555_aryo_activity_log`
  ADD PRIMARY KEY (`histid`);

--
-- Index pour la table `wp_784555_commentmeta`
--
ALTER TABLE `wp_784555_commentmeta`
  ADD PRIMARY KEY (`meta_id`),
  ADD KEY `comment_id` (`comment_id`),
  ADD KEY `meta_key` (`meta_key`(191));

--
-- Index pour la table `wp_784555_comments`
--
ALTER TABLE `wp_784555_comments`
  ADD PRIMARY KEY (`comment_ID`),
  ADD KEY `comment_post_ID` (`comment_post_ID`),
  ADD KEY `comment_approved_date_gmt` (`comment_approved`,`comment_date_gmt`),
  ADD KEY `comment_date_gmt` (`comment_date_gmt`),
  ADD KEY `comment_parent` (`comment_parent`),
  ADD KEY `comment_author_email` (`comment_author_email`(10));

--
-- Index pour la table `wp_784555_e_events`
--
ALTER TABLE `wp_784555_e_events`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_at_index` (`created_at`);

--
-- Index pour la table `wp_784555_fbv`
--
ALTER TABLE `wp_784555_fbv`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `id` (`id`);

--
-- Index pour la table `wp_784555_fbv_attachment_folder`
--
ALTER TABLE `wp_784555_fbv_attachment_folder`
  ADD UNIQUE KEY `folder_id` (`folder_id`,`attachment_id`);

--
-- Index pour la table `wp_784555_links`
--
ALTER TABLE `wp_784555_links`
  ADD PRIMARY KEY (`link_id`),
  ADD KEY `link_visible` (`link_visible`);

--
-- Index pour la table `wp_784555_mapsvg6_maps`
--
ALTER TABLE `wp_784555_mapsvg6_maps`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_mapsvg6_objects_1`
--
ALTER TABLE `wp_784555_mapsvg6_objects_1`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_mapsvg6_objects_2`
--
ALTER TABLE `wp_784555_mapsvg6_objects_2`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_mapsvg6_objects_3`
--
ALTER TABLE `wp_784555_mapsvg6_objects_3`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_mapsvg6_objects_4`
--
ALTER TABLE `wp_784555_mapsvg6_objects_4`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_mapsvg6_objects_5`
--
ALTER TABLE `wp_784555_mapsvg6_objects_5`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_mapsvg6_objects_6`
--
ALTER TABLE `wp_784555_mapsvg6_objects_6`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_mapsvg6_objects_7`
--
ALTER TABLE `wp_784555_mapsvg6_objects_7`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_mapsvg6_objects_8`
--
ALTER TABLE `wp_784555_mapsvg6_objects_8`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_mapsvg6_objects_9`
--
ALTER TABLE `wp_784555_mapsvg6_objects_9`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_mapsvg6_objects_10`
--
ALTER TABLE `wp_784555_mapsvg6_objects_10`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_mapsvg6_objects_11`
--
ALTER TABLE `wp_784555_mapsvg6_objects_11`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_mapsvg6_posts_page`
--
ALTER TABLE `wp_784555_mapsvg6_posts_page`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `wp_784555_mapsvg6_posts_page` ADD FULLTEXT KEY `_keywords` (`location_address`);

--
-- Index pour la table `wp_784555_mapsvg6_r2o`
--
ALTER TABLE `wp_784555_mapsvg6_r2o`
  ADD KEY `objects_table` (`objects_table`,`regions_table`,`region_id`);

--
-- Index pour la table `wp_784555_mapsvg6_regions_1`
--
ALTER TABLE `wp_784555_mapsvg6_regions_1`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `wp_784555_mapsvg6_regions_1` ADD FULLTEXT KEY `_keywords` (`id`,`title`,`population`);

--
-- Index pour la table `wp_784555_mapsvg6_regions_2`
--
ALTER TABLE `wp_784555_mapsvg6_regions_2`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `wp_784555_mapsvg6_regions_2` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `wp_784555_mapsvg6_regions_3`
--
ALTER TABLE `wp_784555_mapsvg6_regions_3`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `wp_784555_mapsvg6_regions_3` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `wp_784555_mapsvg6_regions_4`
--
ALTER TABLE `wp_784555_mapsvg6_regions_4`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `wp_784555_mapsvg6_regions_4` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `wp_784555_mapsvg6_regions_5`
--
ALTER TABLE `wp_784555_mapsvg6_regions_5`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `wp_784555_mapsvg6_regions_5` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `wp_784555_mapsvg6_regions_6`
--
ALTER TABLE `wp_784555_mapsvg6_regions_6`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `wp_784555_mapsvg6_regions_6` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `wp_784555_mapsvg6_regions_7`
--
ALTER TABLE `wp_784555_mapsvg6_regions_7`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `wp_784555_mapsvg6_regions_7` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `wp_784555_mapsvg6_regions_8`
--
ALTER TABLE `wp_784555_mapsvg6_regions_8`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `wp_784555_mapsvg6_regions_8` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `wp_784555_mapsvg6_regions_9`
--
ALTER TABLE `wp_784555_mapsvg6_regions_9`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `wp_784555_mapsvg6_regions_9` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `wp_784555_mapsvg6_regions_10`
--
ALTER TABLE `wp_784555_mapsvg6_regions_10`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `wp_784555_mapsvg6_regions_10` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `wp_784555_mapsvg6_regions_11`
--
ALTER TABLE `wp_784555_mapsvg6_regions_11`
  ADD PRIMARY KEY (`id`);
ALTER TABLE `wp_784555_mapsvg6_regions_11` ADD FULLTEXT KEY `_keywords` (`id`,`title`);

--
-- Index pour la table `wp_784555_mapsvg6_schema`
--
ALTER TABLE `wp_784555_mapsvg6_schema`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_mapsvg6_settings`
--
ALTER TABLE `wp_784555_mapsvg6_settings`
  ADD PRIMARY KEY (`key`);

--
-- Index pour la table `wp_784555_mec_attendees`
--
ALTER TABLE `wp_784555_mec_attendees`
  ADD PRIMARY KEY (`attendee_id`);

--
-- Index pour la table `wp_784555_mec_bookings`
--
ALTER TABLE `wp_784555_mec_bookings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `event_id` (`event_id`,`ticket_ids`,`status`,`confirmed`,`verified`,`date`),
  ADD KEY `booking_id` (`booking_id`),
  ADD KEY `timestamp` (`timestamp`),
  ADD KEY `user_id` (`user_id`);

--
-- Index pour la table `wp_784555_mec_dates`
--
ALTER TABLE `wp_784555_mec_dates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `post_id` (`post_id`),
  ADD KEY `tstart` (`tstart`),
  ADD KEY `tend` (`tend`);

--
-- Index pour la table `wp_784555_mec_events`
--
ALTER TABLE `wp_784555_mec_events`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `ID` (`id`),
  ADD UNIQUE KEY `post_id` (`post_id`),
  ADD KEY `start` (`start`,`end`,`repeat`,`rinterval`,`year`,`month`,`day`,`week`,`weekday`,`weekdays`,`time_start`,`time_end`);

--
-- Index pour la table `wp_784555_mec_occurrences`
--
ALTER TABLE `wp_784555_mec_occurrences`
  ADD PRIMARY KEY (`id`),
  ADD KEY `post_id` (`post_id`),
  ADD KEY `occurrence` (`occurrence`);

--
-- Index pour la table `wp_784555_mec_users`
--
ALTER TABLE `wp_784555_mec_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Index pour la table `wp_784555_newsletter`
--
ALTER TABLE `wp_784555_newsletter`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `wp_user_id` (`wp_user_id`);

--
-- Index pour la table `wp_784555_newsletter_emails`
--
ALTER TABLE `wp_784555_newsletter_emails`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_newsletter_sent`
--
ALTER TABLE `wp_784555_newsletter_sent`
  ADD PRIMARY KEY (`email_id`,`user_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `email_id` (`email_id`);

--
-- Index pour la table `wp_784555_newsletter_stats`
--
ALTER TABLE `wp_784555_newsletter_stats`
  ADD PRIMARY KEY (`id`),
  ADD KEY `email_id` (`email_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Index pour la table `wp_784555_newsletter_user_logs`
--
ALTER TABLE `wp_784555_newsletter_user_logs`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_ninja_table_items`
--
ALTER TABLE `wp_784555_ninja_table_items`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_options`
--
ALTER TABLE `wp_784555_options`
  ADD PRIMARY KEY (`option_id`),
  ADD UNIQUE KEY `option_name` (`option_name`),
  ADD KEY `autoload` (`autoload`);

--
-- Index pour la table `wp_784555_postmeta`
--
ALTER TABLE `wp_784555_postmeta`
  ADD PRIMARY KEY (`meta_id`),
  ADD KEY `post_id` (`post_id`),
  ADD KEY `meta_key` (`meta_key`(191));

--
-- Index pour la table `wp_784555_posts`
--
ALTER TABLE `wp_784555_posts`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `post_name` (`post_name`(191)),
  ADD KEY `type_status_date` (`post_type`,`post_status`,`post_date`,`ID`),
  ADD KEY `post_parent` (`post_parent`),
  ADD KEY `post_author` (`post_author`);

--
-- Index pour la table `wp_784555_searchwp_index`
--
ALTER TABLE `wp_784555_searchwp_index`
  ADD PRIMARY KEY (`indexid`),
  ADD KEY `source_idx` (`source`),
  ADD KEY `token_idx` (`token`),
  ADD KEY `entry_idx` (`id`,`source`,`site`),
  ADD KEY `attribute_idx` (`attribute`);

--
-- Index pour la table `wp_784555_searchwp_log`
--
ALTER TABLE `wp_784555_searchwp_log`
  ADD PRIMARY KEY (`logid`),
  ADD KEY `site_idx` (`site`),
  ADD KEY `engine_idx` (`engine`),
  ADD KEY `query_idx` (`query`);

--
-- Index pour la table `wp_784555_searchwp_status`
--
ALTER TABLE `wp_784555_searchwp_status`
  ADD PRIMARY KEY (`statusid`),
  ADD KEY `id_idx` (`id`),
  ADD KEY `site_idx` (`site`),
  ADD KEY `source_idx` (`source`);

--
-- Index pour la table `wp_784555_searchwp_tokens`
--
ALTER TABLE `wp_784555_searchwp_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `token` (`token`),
  ADD KEY `token_idx` (`token`(2)),
  ADD KEY `stem_idx` (`stem`(2));

--
-- Index pour la table `wp_784555_termmeta`
--
ALTER TABLE `wp_784555_termmeta`
  ADD PRIMARY KEY (`meta_id`),
  ADD KEY `term_id` (`term_id`),
  ADD KEY `meta_key` (`meta_key`(191));

--
-- Index pour la table `wp_784555_terms`
--
ALTER TABLE `wp_784555_terms`
  ADD PRIMARY KEY (`term_id`),
  ADD KEY `slug` (`slug`(191)),
  ADD KEY `name` (`name`(191));

--
-- Index pour la table `wp_784555_term_relationships`
--
ALTER TABLE `wp_784555_term_relationships`
  ADD PRIMARY KEY (`object_id`,`term_taxonomy_id`),
  ADD KEY `term_taxonomy_id` (`term_taxonomy_id`);

--
-- Index pour la table `wp_784555_term_taxonomy`
--
ALTER TABLE `wp_784555_term_taxonomy`
  ADD PRIMARY KEY (`term_taxonomy_id`),
  ADD UNIQUE KEY `term_id_taxonomy` (`term_id`,`taxonomy`),
  ADD KEY `taxonomy` (`taxonomy`);

--
-- Index pour la table `wp_784555_um_metadata`
--
ALTER TABLE `wp_784555_um_metadata`
  ADD PRIMARY KEY (`umeta_id`),
  ADD KEY `user_id_indx` (`user_id`),
  ADD KEY `meta_key_indx` (`um_key`),
  ADD KEY `meta_value_indx` (`um_value`(191));

--
-- Index pour la table `wp_784555_um_notifications`
--
ALTER TABLE `wp_784555_um_notifications`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_usermeta`
--
ALTER TABLE `wp_784555_usermeta`
  ADD PRIMARY KEY (`umeta_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `meta_key` (`meta_key`(191));

--
-- Index pour la table `wp_784555_users`
--
ALTER TABLE `wp_784555_users`
  ADD PRIMARY KEY (`ID`),
  ADD KEY `user_login_key` (`user_login`),
  ADD KEY `user_nicename` (`user_nicename`),
  ADD KEY `user_email` (`user_email`);

--
-- Index pour la table `wp_784555_wfu_dbxqueue`
--
ALTER TABLE `wp_784555_wfu_dbxqueue`
  ADD PRIMARY KEY (`iddbxqueue`);

--
-- Index pour la table `wp_784555_wfu_log`
--
ALTER TABLE `wp_784555_wfu_log`
  ADD PRIMARY KEY (`idlog`),
  ADD KEY `uploadid` (`uploadid`);

--
-- Index pour la table `wp_784555_wfu_userdata`
--
ALTER TABLE `wp_784555_wfu_userdata`
  ADD PRIMARY KEY (`iduserdata`),
  ADD KEY `uploadid` (`uploadid`);

--
-- Index pour la table `wp_784555_wpdatacharts`
--
ALTER TABLE `wp_784555_wpdatacharts`
  ADD UNIQUE KEY `id` (`id`);

--
-- Index pour la table `wp_784555_wpdatatables`
--
ALTER TABLE `wp_784555_wpdatatables`
  ADD UNIQUE KEY `id` (`id`);

--
-- Index pour la table `wp_784555_wpdatatables_columns`
--
ALTER TABLE `wp_784555_wpdatatables_columns`
  ADD UNIQUE KEY `id` (`id`);

--
-- Index pour la table `wp_784555_wpdatatables_rows`
--
ALTER TABLE `wp_784555_wpdatatables_rows`
  ADD UNIQUE KEY `id` (`id`);

--
-- Index pour la table `wp_784555_wpdatatable_6`
--
ALTER TABLE `wp_784555_wpdatatable_6`
  ADD UNIQUE KEY `wdt_ID` (`wdt_ID`);

--
-- Index pour la table `wp_784555_wpfm_backup`
--
ALTER TABLE `wp_784555_wpfm_backup`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_wpforms_entries`
--
ALTER TABLE `wp_784555_wpforms_entries`
  ADD PRIMARY KEY (`entry_id`),
  ADD KEY `form_id` (`form_id`);

--
-- Index pour la table `wp_784555_wpforms_entry_fields`
--
ALTER TABLE `wp_784555_wpforms_entry_fields`
  ADD PRIMARY KEY (`id`),
  ADD KEY `entry_id` (`entry_id`),
  ADD KEY `form_id` (`form_id`),
  ADD KEY `field_id` (`field_id`);

--
-- Index pour la table `wp_784555_wpforms_entry_meta`
--
ALTER TABLE `wp_784555_wpforms_entry_meta`
  ADD PRIMARY KEY (`id`),
  ADD KEY `entry_id` (`entry_id`);

--
-- Index pour la table `wp_784555_wpforms_tasks_meta`
--
ALTER TABLE `wp_784555_wpforms_tasks_meta`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_wpmailsmtp_debug_events`
--
ALTER TABLE `wp_784555_wpmailsmtp_debug_events`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_wpmailsmtp_tasks_meta`
--
ALTER TABLE `wp_784555_wpmailsmtp_tasks_meta`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `wp_784555_yoast_indexable`
--
ALTER TABLE `wp_784555_yoast_indexable`
  ADD PRIMARY KEY (`id`),
  ADD KEY `object_type_and_sub_type` (`object_type`,`object_sub_type`),
  ADD KEY `object_id_and_type` (`object_id`,`object_type`),
  ADD KEY `permalink_hash_and_object_type` (`permalink_hash`,`object_type`),
  ADD KEY `subpages` (`post_parent`,`object_type`,`post_status`,`object_id`),
  ADD KEY `prominent_words` (`prominent_words_version`,`object_type`,`object_sub_type`,`post_status`),
  ADD KEY `published_sitemap_index` (`object_published_at`,`is_robots_noindex`,`object_type`,`object_sub_type`);

--
-- Index pour la table `wp_784555_yoast_indexable_hierarchy`
--
ALTER TABLE `wp_784555_yoast_indexable_hierarchy`
  ADD PRIMARY KEY (`indexable_id`,`ancestor_id`),
  ADD KEY `indexable_id` (`indexable_id`),
  ADD KEY `ancestor_id` (`ancestor_id`),
  ADD KEY `depth` (`depth`);

--
-- Index pour la table `wp_784555_yoast_migrations`
--
ALTER TABLE `wp_784555_yoast_migrations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `wp_784555_yoast_migrations_version` (`version`);

--
-- Index pour la table `wp_784555_yoast_primary_term`
--
ALTER TABLE `wp_784555_yoast_primary_term`
  ADD PRIMARY KEY (`id`),
  ADD KEY `post_taxonomy` (`post_id`,`taxonomy`),
  ADD KEY `post_term` (`post_id`,`term_id`);

--
-- Index pour la table `wp_784555_yoast_seo_links`
--
ALTER TABLE `wp_784555_yoast_seo_links`
  ADD PRIMARY KEY (`id`),
  ADD KEY `link_direction` (`post_id`,`type`),
  ADD KEY `indexable_link_direction` (`indexable_id`,`type`);

--
-- Index pour la table `_mig_queue_failures`
--
ALTER TABLE `_mig_queue_failures`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `_mig_queue_jobs`
--
ALTER TABLE `_mig_queue_jobs`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT pour les tables déchargées
--

--
-- AUTO_INCREMENT pour la table `lmczp_actionscheduler_actions`
--
ALTER TABLE `lmczp_actionscheduler_actions`
  MODIFY `action_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5510;

--
-- AUTO_INCREMENT pour la table `lmczp_actionscheduler_claims`
--
ALTER TABLE `lmczp_actionscheduler_claims`
  MODIFY `claim_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1169027;

--
-- AUTO_INCREMENT pour la table `lmczp_actionscheduler_groups`
--
ALTER TABLE `lmczp_actionscheduler_groups`
  MODIFY `group_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT pour la table `lmczp_actionscheduler_logs`
--
ALTER TABLE `lmczp_actionscheduler_logs`
  MODIFY `log_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20241;

--
-- AUTO_INCREMENT pour la table `lmczp_ac_segments`
--
ALTER TABLE `lmczp_ac_segments`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_admin_columns`
--
ALTER TABLE `lmczp_admin_columns`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT pour la table `lmczp_aiowps_audit_log`
--
ALTER TABLE `lmczp_aiowps_audit_log`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3250;

--
-- AUTO_INCREMENT pour la table `lmczp_aiowps_debug_log`
--
ALTER TABLE `lmczp_aiowps_debug_log`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_aiowps_events`
--
ALTER TABLE `lmczp_aiowps_events`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_aiowps_global_meta`
--
ALTER TABLE `lmczp_aiowps_global_meta`
  MODIFY `meta_id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_aiowps_logged_in_users`
--
ALTER TABLE `lmczp_aiowps_logged_in_users`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=121;

--
-- AUTO_INCREMENT pour la table `lmczp_aiowps_login_lockdown`
--
ALTER TABLE `lmczp_aiowps_login_lockdown`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=47;

--
-- AUTO_INCREMENT pour la table `lmczp_aiowps_message_store`
--
ALTER TABLE `lmczp_aiowps_message_store`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_aiowps_permanent_block`
--
ALTER TABLE `lmczp_aiowps_permanent_block`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `lmczp_commentmeta`
--
ALTER TABLE `lmczp_commentmeta`
  MODIFY `meta_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=60;

--
-- AUTO_INCREMENT pour la table `lmczp_comments`
--
ALTER TABLE `lmczp_comments`
  MODIFY `comment_ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT pour la table `lmczp_e_events`
--
ALTER TABLE `lmczp_e_events`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT pour la table `lmczp_jet_post_types`
--
ALTER TABLE `lmczp_jet_post_types`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_jet_taxonomies`
--
ALTER TABLE `lmczp_jet_taxonomies`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_links`
--
ALTER TABLE `lmczp_links`
  MODIFY `link_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_mapsvg6_maps`
--
ALTER TABLE `lmczp_mapsvg6_maps`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `lmczp_mapsvg6_objects_0`
--
ALTER TABLE `lmczp_mapsvg6_objects_0`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_mapsvg6_objects_1`
--
ALTER TABLE `lmczp_mapsvg6_objects_1`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_mapsvg6_objects_2`
--
ALTER TABLE `lmczp_mapsvg6_objects_2`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_mapsvg6_objects_3`
--
ALTER TABLE `lmczp_mapsvg6_objects_3`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_mapsvg6_objects_4`
--
ALTER TABLE `lmczp_mapsvg6_objects_4`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_mapsvg6_schema`
--
ALTER TABLE `lmczp_mapsvg6_schema`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT pour la table `lmczp_mec_attendees`
--
ALTER TABLE `lmczp_mec_attendees`
  MODIFY `attendee_id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT pour la table `lmczp_mec_bookings`
--
ALTER TABLE `lmczp_mec_bookings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_mec_dates`
--
ALTER TABLE `lmczp_mec_dates`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=833777;

--
-- AUTO_INCREMENT pour la table `lmczp_mec_events`
--
ALTER TABLE `lmczp_mec_events`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=79;

--
-- AUTO_INCREMENT pour la table `lmczp_mec_occurrences`
--
ALTER TABLE `lmczp_mec_occurrences`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_mec_users`
--
ALTER TABLE `lmczp_mec_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1000000;

--
-- AUTO_INCREMENT pour la table `lmczp_options`
--
ALTER TABLE `lmczp_options`
  MODIFY `option_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6625855;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_documents`
--
ALTER TABLE `lmczp_pacad_documents`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=698;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_document_effects`
--
ALTER TABLE `lmczp_pacad_document_effects`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_document_tags`
--
ALTER TABLE `lmczp_pacad_document_tags`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_document_zones`
--
ALTER TABLE `lmczp_pacad_document_zones`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_donation_extensions`
--
ALTER TABLE `lmczp_pacad_donation_extensions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_pcc_indicators_data`
--
ALTER TABLE `lmczp_pacad_pcc_indicators_data`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_projects`
--
ALTER TABLE `lmczp_pacad_projects`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=119;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_project_agencies`
--
ALTER TABLE `lmczp_pacad_project_agencies`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=169;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_project_donations`
--
ALTER TABLE `lmczp_pacad_project_donations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=165;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_project_donation_budgets`
--
ALTER TABLE `lmczp_pacad_project_donation_budgets`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=538;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_project_effects`
--
ALTER TABLE `lmczp_pacad_project_effects`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=213;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_project_extensions`
--
ALTER TABLE `lmczp_pacad_project_extensions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_project_fundings`
--
ALTER TABLE `lmczp_pacad_project_fundings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=147;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_project_info`
--
ALTER TABLE `lmczp_pacad_project_info`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=172;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_project_zones`
--
ALTER TABLE `lmczp_pacad_project_zones`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2883;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_sms_alerts_reports`
--
ALTER TABLE `lmczp_pacad_sms_alerts_reports`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `lmczp_pacad_sms_alerts_report_data`
--
ALTER TABLE `lmczp_pacad_sms_alerts_report_data`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT pour la table `lmczp_postmeta`
--
ALTER TABLE `lmczp_postmeta`
  MODIFY `meta_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=55437;

--
-- AUTO_INCREMENT pour la table `lmczp_posts`
--
ALTER TABLE `lmczp_posts`
  MODIFY `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7386;

--
-- AUTO_INCREMENT pour la table `lmczp_rank_math_internal_links`
--
ALTER TABLE `lmczp_rank_math_internal_links`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1634;

--
-- AUTO_INCREMENT pour la table `lmczp_snippets`
--
ALTER TABLE `lmczp_snippets`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT pour la table `lmczp_tec_events`
--
ALTER TABLE `lmczp_tec_events`
  MODIFY `event_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_tec_occurrences`
--
ALTER TABLE `lmczp_tec_occurrences`
  MODIFY `occurrence_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_tec_series_relationships`
--
ALTER TABLE `lmczp_tec_series_relationships`
  MODIFY `relationship_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_termmeta`
--
ALTER TABLE `lmczp_termmeta`
  MODIFY `meta_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=555;

--
-- AUTO_INCREMENT pour la table `lmczp_terms`
--
ALTER TABLE `lmczp_terms`
  MODIFY `term_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=748;

--
-- AUTO_INCREMENT pour la table `lmczp_term_taxonomy`
--
ALTER TABLE `lmczp_term_taxonomy`
  MODIFY `term_taxonomy_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=748;

--
-- AUTO_INCREMENT pour la table `lmczp_um_metadata`
--
ALTER TABLE `lmczp_um_metadata`
  MODIFY `umeta_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_usermeta`
--
ALTER TABLE `lmczp_usermeta`
  MODIFY `umeta_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=145570;

--
-- AUTO_INCREMENT pour la table `lmczp_users`
--
ALTER TABLE `lmczp_users`
  MODIFY `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3543;

--
-- AUTO_INCREMENT pour la table `lmczp_wpda_org_chart_popup_theme`
--
ALTER TABLE `lmczp_wpda_org_chart_popup_theme`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT pour la table `lmczp_wpda_org_chart_theme`
--
ALTER TABLE `lmczp_wpda_org_chart_theme`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=51;

--
-- AUTO_INCREMENT pour la table `lmczp_wpda_org_chart_tree`
--
ALTER TABLE `lmczp_wpda_org_chart_tree`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT pour la table `lmczp_wpforms_entries`
--
ALTER TABLE `lmczp_wpforms_entries`
  MODIFY `entry_id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=97;

--
-- AUTO_INCREMENT pour la table `lmczp_wpforms_entry_fields`
--
ALTER TABLE `lmczp_wpforms_entry_fields`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=391;

--
-- AUTO_INCREMENT pour la table `lmczp_wpforms_entry_meta`
--
ALTER TABLE `lmczp_wpforms_entry_meta`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT pour la table `lmczp_wpforms_tasks_meta`
--
ALTER TABLE `lmczp_wpforms_tasks_meta`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1240;

--
-- AUTO_INCREMENT pour la table `lmczp_wpie_template`
--
ALTER TABLE `lmczp_wpie_template`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `lmczp_wpinventory_category`
--
ALTER TABLE `lmczp_wpinventory_category`
  MODIFY `category_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `lmczp_wpinventory_image`
--
ALTER TABLE `lmczp_wpinventory_image`
  MODIFY `image_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT pour la table `lmczp_wpinventory_item`
--
ALTER TABLE `lmczp_wpinventory_item`
  MODIFY `inventory_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `lmczp_wpinventory_label`
--
ALTER TABLE `lmczp_wpinventory_label`
  MODIFY `label_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_wpinventory_media`
--
ALTER TABLE `lmczp_wpinventory_media`
  MODIFY `media_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7329;

--
-- AUTO_INCREMENT pour la table `lmczp_wpinventory_reservation`
--
ALTER TABLE `lmczp_wpinventory_reservation`
  MODIFY `reservation_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_wpinventory_reservation_item`
--
ALTER TABLE `lmczp_wpinventory_reservation_item`
  MODIFY `reservation_item_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `lmczp_wpinventory_status`
--
ALTER TABLE `lmczp_wpinventory_status`
  MODIFY `status_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `lmczp_wpmailsmtp_debug_events`
--
ALTER TABLE `lmczp_wpmailsmtp_debug_events`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1112;

--
-- AUTO_INCREMENT pour la table `lmczp_wpmailsmtp_tasks_meta`
--
ALTER TABLE `lmczp_wpmailsmtp_tasks_meta`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=167;

--
-- AUTO_INCREMENT pour la table `wp_784555_actionscheduler_actions`
--
ALTER TABLE `wp_784555_actionscheduler_actions`
  MODIFY `action_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1247;

--
-- AUTO_INCREMENT pour la table `wp_784555_actionscheduler_claims`
--
ALTER TABLE `wp_784555_actionscheduler_claims`
  MODIFY `claim_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=79797;

--
-- AUTO_INCREMENT pour la table `wp_784555_actionscheduler_groups`
--
ALTER TABLE `wp_784555_actionscheduler_groups`
  MODIFY `group_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `wp_784555_actionscheduler_logs`
--
ALTER TABLE `wp_784555_actionscheduler_logs`
  MODIFY `log_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3665;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_action`
--
ALTER TABLE `wp_784555_acym_action`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_automation`
--
ALTER TABLE `wp_784555_acym_automation`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_campaign`
--
ALTER TABLE `wp_784555_acym_campaign`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_condition`
--
ALTER TABLE `wp_784555_acym_condition`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_custom_zone`
--
ALTER TABLE `wp_784555_acym_custom_zone`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_field`
--
ALTER TABLE `wp_784555_acym_field`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_followup`
--
ALTER TABLE `wp_784555_acym_followup`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_form`
--
ALTER TABLE `wp_784555_acym_form`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_list`
--
ALTER TABLE `wp_784555_acym_list`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_mail`
--
ALTER TABLE `wp_784555_acym_mail`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_mail_override`
--
ALTER TABLE `wp_784555_acym_mail_override`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_plugin`
--
ALTER TABLE `wp_784555_acym_plugin`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_rule`
--
ALTER TABLE `wp_784555_acym_rule`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_segment`
--
ALTER TABLE `wp_784555_acym_segment`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_step`
--
ALTER TABLE `wp_784555_acym_step`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_url`
--
ALTER TABLE `wp_784555_acym_url`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_acym_user`
--
ALTER TABLE `wp_784555_acym_user`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `wp_784555_aryo_activity_log`
--
ALTER TABLE `wp_784555_aryo_activity_log`
  MODIFY `histid` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2432;

--
-- AUTO_INCREMENT pour la table `wp_784555_commentmeta`
--
ALTER TABLE `wp_784555_commentmeta`
  MODIFY `meta_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT pour la table `wp_784555_comments`
--
ALTER TABLE `wp_784555_comments`
  MODIFY `comment_ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT pour la table `wp_784555_e_events`
--
ALTER TABLE `wp_784555_e_events`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_fbv`
--
ALTER TABLE `wp_784555_fbv`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `wp_784555_links`
--
ALTER TABLE `wp_784555_links`
  MODIFY `link_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_maps`
--
ALTER TABLE `wp_784555_mapsvg6_maps`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_objects_1`
--
ALTER TABLE `wp_784555_mapsvg6_objects_1`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_objects_2`
--
ALTER TABLE `wp_784555_mapsvg6_objects_2`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_objects_3`
--
ALTER TABLE `wp_784555_mapsvg6_objects_3`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_objects_4`
--
ALTER TABLE `wp_784555_mapsvg6_objects_4`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_objects_5`
--
ALTER TABLE `wp_784555_mapsvg6_objects_5`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_objects_6`
--
ALTER TABLE `wp_784555_mapsvg6_objects_6`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_objects_7`
--
ALTER TABLE `wp_784555_mapsvg6_objects_7`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_objects_8`
--
ALTER TABLE `wp_784555_mapsvg6_objects_8`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_objects_9`
--
ALTER TABLE `wp_784555_mapsvg6_objects_9`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_objects_10`
--
ALTER TABLE `wp_784555_mapsvg6_objects_10`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_objects_11`
--
ALTER TABLE `wp_784555_mapsvg6_objects_11`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_posts_page`
--
ALTER TABLE `wp_784555_mapsvg6_posts_page`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mapsvg6_schema`
--
ALTER TABLE `wp_784555_mapsvg6_schema`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT pour la table `wp_784555_mec_attendees`
--
ALTER TABLE `wp_784555_mec_attendees`
  MODIFY `attendee_id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=66;

--
-- AUTO_INCREMENT pour la table `wp_784555_mec_bookings`
--
ALTER TABLE `wp_784555_mec_bookings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mec_dates`
--
ALTER TABLE `wp_784555_mec_dates`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10283;

--
-- AUTO_INCREMENT pour la table `wp_784555_mec_events`
--
ALTER TABLE `wp_784555_mec_events`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT pour la table `wp_784555_mec_occurrences`
--
ALTER TABLE `wp_784555_mec_occurrences`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_mec_users`
--
ALTER TABLE `wp_784555_mec_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1000000;

--
-- AUTO_INCREMENT pour la table `wp_784555_newsletter`
--
ALTER TABLE `wp_784555_newsletter`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT pour la table `wp_784555_newsletter_emails`
--
ALTER TABLE `wp_784555_newsletter_emails`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_newsletter_stats`
--
ALTER TABLE `wp_784555_newsletter_stats`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_newsletter_user_logs`
--
ALTER TABLE `wp_784555_newsletter_user_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_ninja_table_items`
--
ALTER TABLE `wp_784555_ninja_table_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT pour la table `wp_784555_options`
--
ALTER TABLE `wp_784555_options`
  MODIFY `option_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=163230;

--
-- AUTO_INCREMENT pour la table `wp_784555_postmeta`
--
ALTER TABLE `wp_784555_postmeta`
  MODIFY `meta_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18890;

--
-- AUTO_INCREMENT pour la table `wp_784555_posts`
--
ALTER TABLE `wp_784555_posts`
  MODIFY `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3582;

--
-- AUTO_INCREMENT pour la table `wp_784555_searchwp_index`
--
ALTER TABLE `wp_784555_searchwp_index`
  MODIFY `indexid` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3375;

--
-- AUTO_INCREMENT pour la table `wp_784555_searchwp_log`
--
ALTER TABLE `wp_784555_searchwp_log`
  MODIFY `logid` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT pour la table `wp_784555_searchwp_status`
--
ALTER TABLE `wp_784555_searchwp_status`
  MODIFY `statusid` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=216;

--
-- AUTO_INCREMENT pour la table `wp_784555_searchwp_tokens`
--
ALTER TABLE `wp_784555_searchwp_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Canonical ID for this token', AUTO_INCREMENT=1195;

--
-- AUTO_INCREMENT pour la table `wp_784555_termmeta`
--
ALTER TABLE `wp_784555_termmeta`
  MODIFY `meta_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=60;

--
-- AUTO_INCREMENT pour la table `wp_784555_terms`
--
ALTER TABLE `wp_784555_terms`
  MODIFY `term_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=74;

--
-- AUTO_INCREMENT pour la table `wp_784555_term_taxonomy`
--
ALTER TABLE `wp_784555_term_taxonomy`
  MODIFY `term_taxonomy_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=74;

--
-- AUTO_INCREMENT pour la table `wp_784555_um_metadata`
--
ALTER TABLE `wp_784555_um_metadata`
  MODIFY `umeta_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_um_notifications`
--
ALTER TABLE `wp_784555_um_notifications`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_usermeta`
--
ALTER TABLE `wp_784555_usermeta`
  MODIFY `umeta_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=773;

--
-- AUTO_INCREMENT pour la table `wp_784555_users`
--
ALTER TABLE `wp_784555_users`
  MODIFY `ID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT pour la table `wp_784555_wfu_dbxqueue`
--
ALTER TABLE `wp_784555_wfu_dbxqueue`
  MODIFY `iddbxqueue` mediumint(9) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_wfu_log`
--
ALTER TABLE `wp_784555_wfu_log`
  MODIFY `idlog` mediumint(9) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_wfu_userdata`
--
ALTER TABLE `wp_784555_wfu_userdata`
  MODIFY `iduserdata` mediumint(9) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_wpdatacharts`
--
ALTER TABLE `wp_784555_wpdatacharts`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `wp_784555_wpdatatables`
--
ALTER TABLE `wp_784555_wpdatatables`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT pour la table `wp_784555_wpdatatables_columns`
--
ALTER TABLE `wp_784555_wpdatatables_columns`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=724;

--
-- AUTO_INCREMENT pour la table `wp_784555_wpdatatables_rows`
--
ALTER TABLE `wp_784555_wpdatatables_rows`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT pour la table `wp_784555_wpdatatable_6`
--
ALTER TABLE `wp_784555_wpdatatable_6`
  MODIFY `wdt_ID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT pour la table `wp_784555_wpfm_backup`
--
ALTER TABLE `wp_784555_wpfm_backup`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_wpforms_entries`
--
ALTER TABLE `wp_784555_wpforms_entries`
  MODIFY `entry_id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT pour la table `wp_784555_wpforms_entry_fields`
--
ALTER TABLE `wp_784555_wpforms_entry_fields`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT pour la table `wp_784555_wpforms_entry_meta`
--
ALTER TABLE `wp_784555_wpforms_entry_meta`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT pour la table `wp_784555_wpforms_tasks_meta`
--
ALTER TABLE `wp_784555_wpforms_tasks_meta`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=158;

--
-- AUTO_INCREMENT pour la table `wp_784555_wpmailsmtp_debug_events`
--
ALTER TABLE `wp_784555_wpmailsmtp_debug_events`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_wpmailsmtp_tasks_meta`
--
ALTER TABLE `wp_784555_wpmailsmtp_tasks_meta`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=139;

--
-- AUTO_INCREMENT pour la table `wp_784555_yoast_indexable`
--
ALTER TABLE `wp_784555_yoast_indexable`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT pour la table `wp_784555_yoast_migrations`
--
ALTER TABLE `wp_784555_yoast_migrations`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT pour la table `wp_784555_yoast_primary_term`
--
ALTER TABLE `wp_784555_yoast_primary_term`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `wp_784555_yoast_seo_links`
--
ALTER TABLE `wp_784555_yoast_seo_links`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `_mig_queue_failures`
--
ALTER TABLE `_mig_queue_failures`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `_mig_queue_jobs`
--
ALTER TABLE `_mig_queue_jobs`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `lmczp_pacad_documents`
--
ALTER TABLE `lmczp_pacad_documents`
  ADD CONSTRAINT `pacad_documents_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `lmczp_users` (`ID`),
  ADD CONSTRAINT `pacad_documents_event_id_foreign` FOREIGN KEY (`event_id`) REFERENCES `lmczp_posts` (`ID`) ON DELETE CASCADE,
  ADD CONSTRAINT `pacad_documents_post_id_foreign` FOREIGN KEY (`post_id`) REFERENCES `lmczp_posts` (`ID`) ON DELETE CASCADE;

--
-- Contraintes pour la table `lmczp_pacad_document_effects`
--
ALTER TABLE `lmczp_pacad_document_effects`
  ADD CONSTRAINT `pacad_document_effects_document_id_foreign` FOREIGN KEY (`document_id`) REFERENCES `lmczp_pacad_documents` (`id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `lmczp_pacad_document_tags`
--
ALTER TABLE `lmczp_pacad_document_tags`
  ADD CONSTRAINT `pacad_document_tags_document_id_foreign` FOREIGN KEY (`document_id`) REFERENCES `lmczp_pacad_documents` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pacad_document_tags_tag_id_foreign` FOREIGN KEY (`tag_id`) REFERENCES `lmczp_terms` (`term_id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `lmczp_pacad_document_zones`
--
ALTER TABLE `lmczp_pacad_document_zones`
  ADD CONSTRAINT `pacad_document_zones_commune_id_foreign` FOREIGN KEY (`commune_id`) REFERENCES `lmczp_terms` (`term_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pacad_document_zones_department_id_foreign` FOREIGN KEY (`department_id`) REFERENCES `lmczp_terms` (`term_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pacad_document_zones_document_id_foreign` FOREIGN KEY (`document_id`) REFERENCES `lmczp_pacad_documents` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pacad_document_zones_region_id_foreign` FOREIGN KEY (`region_id`) REFERENCES `lmczp_terms` (`term_id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `lmczp_pacad_donation_extensions`
--
ALTER TABLE `lmczp_pacad_donation_extensions`
  ADD CONSTRAINT `wp_pacad_donation_extensions_donation_id_foreign` FOREIGN KEY (`donation_id`) REFERENCES `lmczp_pacad_project_donations` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_donation_extensions_revision_author_id_foreign` FOREIGN KEY (`revision_author_id`) REFERENCES `lmczp_users` (`ID`);

--
-- Contraintes pour la table `lmczp_pacad_pcc_indicators_data`
--
ALTER TABLE `lmczp_pacad_pcc_indicators_data`
  ADD CONSTRAINT `pacad_pcc_indicators_data_pcc_indicator_id_foreign` FOREIGN KEY (`pcc_indicator_id`) REFERENCES `lmczp_posts` (`ID`) ON DELETE CASCADE;

--
-- Contraintes pour la table `lmczp_pacad_projects`
--
ALTER TABLE `lmczp_pacad_projects`
  ADD CONSTRAINT `wp_pacad_projects_post_id_foreign` FOREIGN KEY (`post_id`) REFERENCES `lmczp_posts` (`ID`) ON DELETE CASCADE;

--
-- Contraintes pour la table `lmczp_pacad_project_agencies`
--
ALTER TABLE `lmczp_pacad_project_agencies`
  ADD CONSTRAINT `wp_pacad_project_agencies_agency_id_foreign` FOREIGN KEY (`agency_id`) REFERENCES `lmczp_terms` (`term_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_agencies_focal_point_id_foreign` FOREIGN KEY (`focal_point_id`) REFERENCES `lmczp_users` (`ID`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_agencies_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `lmczp_pacad_projects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_agencies_revision_author_id_foreign` FOREIGN KEY (`revision_author_id`) REFERENCES `lmczp_users` (`ID`);

--
-- Contraintes pour la table `lmczp_pacad_project_donations`
--
ALTER TABLE `lmczp_pacad_project_donations`
  ADD CONSTRAINT `wp_pacad_project_donations_donor_id_foreign` FOREIGN KEY (`donor_id`) REFERENCES `lmczp_terms` (`term_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_donations_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `lmczp_pacad_projects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_donations_revision_author_id_foreign` FOREIGN KEY (`revision_author_id`) REFERENCES `lmczp_users` (`ID`);

--
-- Contraintes pour la table `lmczp_pacad_project_donation_budgets`
--
ALTER TABLE `lmczp_pacad_project_donation_budgets`
  ADD CONSTRAINT `wp_pacad_project_donation_budgets_donation_id_foreign` FOREIGN KEY (`donation_id`) REFERENCES `lmczp_pacad_project_donations` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_donation_budgets_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `lmczp_pacad_projects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_donation_budgets_revision_author_id_foreign` FOREIGN KEY (`revision_author_id`) REFERENCES `lmczp_users` (`ID`),
  ADD CONSTRAINT `wp_pacad_project_donation_budgets_sdg_id_foreign` FOREIGN KEY (`sdg_id`) REFERENCES `lmczp_terms` (`term_id`);

--
-- Contraintes pour la table `lmczp_pacad_project_effects`
--
ALTER TABLE `lmczp_pacad_project_effects`
  ADD CONSTRAINT `wp_pacad_project_effects_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `lmczp_pacad_projects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_effects_revision_author_id_foreign` FOREIGN KEY (`revision_author_id`) REFERENCES `lmczp_users` (`ID`);

--
-- Contraintes pour la table `lmczp_pacad_project_extensions`
--
ALTER TABLE `lmczp_pacad_project_extensions`
  ADD CONSTRAINT `wp_pacad_project_extensions_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `lmczp_pacad_projects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_extensions_revision_author_id_foreign` FOREIGN KEY (`revision_author_id`) REFERENCES `lmczp_users` (`ID`);

--
-- Contraintes pour la table `lmczp_pacad_project_fundings`
--
ALTER TABLE `lmczp_pacad_project_fundings`
  ADD CONSTRAINT `wp_pacad_project_fundings_agency_id_foreign` FOREIGN KEY (`agency_id`) REFERENCES `lmczp_terms` (`term_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_fundings_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `lmczp_pacad_projects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_fundings_revision_author_id_foreign` FOREIGN KEY (`revision_author_id`) REFERENCES `lmczp_users` (`ID`);

--
-- Contraintes pour la table `lmczp_pacad_project_info`
--
ALTER TABLE `lmczp_pacad_project_info`
  ADD CONSTRAINT `wp_pacad_project_info_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `lmczp_pacad_projects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_info_revision_author_id_foreign` FOREIGN KEY (`revision_author_id`) REFERENCES `lmczp_users` (`ID`);

--
-- Contraintes pour la table `lmczp_pacad_project_zones`
--
ALTER TABLE `lmczp_pacad_project_zones`
  ADD CONSTRAINT `wp_pacad_project_zones_commune_id_foreign` FOREIGN KEY (`commune_id`) REFERENCES `lmczp_terms` (`term_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_zones_department_id_foreign` FOREIGN KEY (`department_id`) REFERENCES `lmczp_terms` (`term_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_zones_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `lmczp_pacad_projects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_zones_region_id_foreign` FOREIGN KEY (`region_id`) REFERENCES `lmczp_terms` (`term_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `wp_pacad_project_zones_revision_author_id_foreign` FOREIGN KEY (`revision_author_id`) REFERENCES `lmczp_users` (`ID`);

--
-- Contraintes pour la table `lmczp_pacad_sms_alerts_reports`
--
ALTER TABLE `lmczp_pacad_sms_alerts_reports`
  ADD CONSTRAINT `pacad_sms_alerts_reports_sent_by_foreign` FOREIGN KEY (`sent_by`) REFERENCES `lmczp_users` (`ID`);

--
-- Contraintes pour la table `lmczp_tec_occurrences`
--
ALTER TABLE `lmczp_tec_occurrences`
  ADD CONSTRAINT `lmczp_tec_occurrences_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `lmczp_tec_events` (`event_id`) ON DELETE CASCADE;

--
-- Contraintes pour la table `wp_784555_acym_action`
--
ALTER TABLE `wp_784555_acym_action`
  ADD CONSTRAINT `fk_wp_784555_acym_action1` FOREIGN KEY (`condition_id`) REFERENCES `wp_784555_acym_condition` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_campaign`
--
ALTER TABLE `wp_784555_acym_campaign`
  ADD CONSTRAINT `fk_wp_784555_acym_campaign_has_mail1` FOREIGN KEY (`mail_id`) REFERENCES `wp_784555_acym_mail` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_condition`
--
ALTER TABLE `wp_784555_acym_condition`
  ADD CONSTRAINT `fk_wp_784555_acym_condition1` FOREIGN KEY (`step_id`) REFERENCES `wp_784555_acym_step` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_followup`
--
ALTER TABLE `wp_784555_acym_followup`
  ADD CONSTRAINT `fk_wp_784555_acym_followup_has_list` FOREIGN KEY (`list_id`) REFERENCES `wp_784555_acym_list` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_followup_has_mail`
--
ALTER TABLE `wp_784555_acym_followup_has_mail`
  ADD CONSTRAINT `fk_wp_784555_acym_mail_has_followup1` FOREIGN KEY (`mail_id`) REFERENCES `wp_784555_acym_mail` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_wp_784555_acym_mail_has_followup2` FOREIGN KEY (`followup_id`) REFERENCES `wp_784555_acym_followup` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_list`
--
ALTER TABLE `wp_784555_acym_list`
  ADD CONSTRAINT `fk_wp_784555_acym_list_has_mail1` FOREIGN KEY (`welcome_id`) REFERENCES `wp_784555_acym_mail` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_wp_784555_acym_list_has_mail2` FOREIGN KEY (`unsubscribe_id`) REFERENCES `wp_784555_acym_mail` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_mail`
--
ALTER TABLE `wp_784555_acym_mail`
  ADD CONSTRAINT `fk_wp_784555_acym_mail1` FOREIGN KEY (`parent_id`) REFERENCES `wp_784555_acym_mail` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_mail_has_list`
--
ALTER TABLE `wp_784555_acym_mail_has_list`
  ADD CONSTRAINT `fk_wp_784555_acym_mail_has_list1` FOREIGN KEY (`mail_id`) REFERENCES `wp_784555_acym_mail` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_wp_784555_acym_mail_has_list2` FOREIGN KEY (`list_id`) REFERENCES `wp_784555_acym_list` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_mail_override`
--
ALTER TABLE `wp_784555_acym_mail_override`
  ADD CONSTRAINT `fk_wp_784555_acym_mail_override1` FOREIGN KEY (`mail_id`) REFERENCES `wp_784555_acym_mail` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_mail_stat`
--
ALTER TABLE `wp_784555_acym_mail_stat`
  ADD CONSTRAINT `fk_wp_784555_acym_mail_stat1` FOREIGN KEY (`mail_id`) REFERENCES `wp_784555_acym_mail` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_queue`
--
ALTER TABLE `wp_784555_acym_queue`
  ADD CONSTRAINT `fk_wp_784555_acym_queue1` FOREIGN KEY (`mail_id`) REFERENCES `wp_784555_acym_mail` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_wp_784555_acym_queue2` FOREIGN KEY (`user_id`) REFERENCES `wp_784555_acym_user` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_step`
--
ALTER TABLE `wp_784555_acym_step`
  ADD CONSTRAINT `fk_wp_784555_acym__step1` FOREIGN KEY (`automation_id`) REFERENCES `wp_784555_acym_automation` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_url_click`
--
ALTER TABLE `wp_784555_acym_url_click`
  ADD CONSTRAINT `fk_wp_784555_acym_url_click_has_mail` FOREIGN KEY (`mail_id`) REFERENCES `wp_784555_acym_mail` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_wp_784555_acym_url_has_url` FOREIGN KEY (`url_id`) REFERENCES `wp_784555_acym_url` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_user_has_field`
--
ALTER TABLE `wp_784555_acym_user_has_field`
  ADD CONSTRAINT `fk_wp_784555_acym_user_has_field1` FOREIGN KEY (`user_id`) REFERENCES `wp_784555_acym_user` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_wp_784555_acym_user_has_field2` FOREIGN KEY (`field_id`) REFERENCES `wp_784555_acym_field` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_user_has_list`
--
ALTER TABLE `wp_784555_acym_user_has_list`
  ADD CONSTRAINT `fk_wp_784555_acym_user_has_list1` FOREIGN KEY (`user_id`) REFERENCES `wp_784555_acym_user` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  ADD CONSTRAINT `fk_wp_784555_acym_user_has_list2` FOREIGN KEY (`list_id`) REFERENCES `wp_784555_acym_list` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;

--
-- Contraintes pour la table `wp_784555_acym_user_stat`
--
ALTER TABLE `wp_784555_acym_user_stat`
  ADD CONSTRAINT `fk_wp_784555_acym_user_stat1` FOREIGN KEY (`mail_id`) REFERENCES `wp_784555_acym_mail` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
