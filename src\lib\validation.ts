// src/lib/validation.ts
import { z } from "zod";

const AgencyDTOSchema = z.object({
  name: z.string().min(1, "Le nom de l'agence est requis"),
  contact: z
    .object({
      name: z.string().optional(),
      title: z.string().optional(),
      email: z.string().email().optional(),
    })
    .optional(),
  received: z.number().optional(),
  disbursed: z.number().optional(),
});

const KoboProjectDTOSchema = z.object({
  id: z.string().or(z.number()),
  title: z.string().min(1, "Le titre est requis"),
  description: z.string().min(1, "La description est requise"),
  type: z.string().optional(),
  status: z.string().optional(),
  progress: z.string().optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  has_extension: z.boolean().optional(),
  extension_dates: z.array(z.string()).optional(),
  agencies: z.array(AgencyDTOSchema).optional(),
  donor: z.string().optional(),
  budget: z.number().optional(),
  mobilized: z.number().optional(),
  disbursed: z.number().optional(),
  effects: z.array(z.string()).optional(),
  products: z.array(z.string()).optional(),
  target_population: z.array(z.string()).optional(),
  sectors: z.array(z.string()).optional(),
  sdgs: z.array(z.string()).optional(),
  regions: z.array(z.string()).optional(),
  departments: z.array(z.string()).optional(),
  communes: z.array(z.string()).optional(),
});

export function validateKoboProject(data: unknown) {
  return KoboProjectDTOSchema.safeParse(data);
}
