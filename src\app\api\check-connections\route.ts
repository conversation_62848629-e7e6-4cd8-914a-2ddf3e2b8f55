import { NextResponse } from "next/server";
import { testMySQLConnection } from "@/lib/prisma";
import { testKoboConnection } from "@/lib/kobo";

export async function GET() {
  try {
    const [mysqlResult, koboResult] = await Promise.all([
      testMySQLConnection(),
      testKoboConnection(),
    ]);
    return NextResponse.json({
      mysql: mysqlResult,
      kobo: koboResult,
    });
  } catch (error) {
    console.error('Erreur dans check-connections :', error);
    return NextResponse.json(
      {
        mysql: { status: "error", message: "Erreur inconnue" },
        kobo: { status: "error", message: "Erreur inconnue" },
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
